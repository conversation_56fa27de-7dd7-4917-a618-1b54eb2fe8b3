// This file is a template and is automatically generated by the setup_vscode.py script.
// Do not edit this file directly.
// 
// Generated from: /home/<USER>/QiruiItem/IsaacLabExtensionTemplate/.vscode/tools/settings.template.json
{
    "files.associations": {
        "*.tpp": "cpp",
        "*.kit": "toml",
        "*.rst": "restructuredtext"
    },
    "editor.rulers": [120],

    // files to be ignored by the linter
    "files.watcherExclude": {
        "**/.git/objects/**": true,
        "**/.git/subtree-cache/**": true,
        "**/node_modules/**": true,
        "**/_isaac_sim/**": true,
        "**/_compiler/**": true
    },
    // Configuration for spelling checker
    "spellright.language": [
        "en-US-10-1."
    ],
    "spellright.documentTypes": [
        "markdown",
        "latex",
        "plaintext",
        "cpp",
        "asciidoc",
        "python",
        "restructuredtext"
    ],
    "cSpell.words": [
        "literalinclude",
        "linenos",
        "instanceable",
        "isaacSim",
        "jacobians",
        "pointcloud",
        "ridgeback",
        "rllib",
        "robomimic",
        "teleoperation",
        "xform",
        "numpy",
        "tensordict",
        "flatcache",
        "physx",
        "dpad",
        "gamepad",
        "linspace",
        "upsampled",
        "downsampled",
        "arange",
        "discretization",
        "trimesh",
        "uninstanceable"
    ],
    // This enables python language server. Seems to work slightly better than jedi:
    "python.languageServer": "Pylance",
    // We use "black" as a formatter:
    "python.formatting.provider": "black",
    "python.formatting.blackArgs": ["--line-length", "120"],
    // Use flake8 for linting
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": true,
    "python.linting.flake8Args": [
        "--max-line-length=120"
    ],
    // Use docstring generator
    "autoDocstring.docstringFormat": "google",
    "autoDocstring.guessTypes": true,
    // Python environment path
    // note: the default interpreter is overridden when user selects a workspace interpreter
    //     in the status bar. For example, the virtual environment python interpreter
    "python.defaultInterpreterPath": "/home/<USER>/anaconda3/envs/QiruiItem/bin/python",

    "python.envFile": "${workspaceFolder}/.env",

    // ROS distribution
    "ros.distro": "noetic",
    // Language specific settings
    "[python]": {
        "editor.tabSize": 4
    },
    "[restructuredtext]": {
        "editor.tabSize": 2
    },
    // Python extra paths
    // Note: this is filled up when vscode is set up for the first time
    "python.analysis.extraPaths": [
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.exporter.urdf",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.exporter.urdf/pip_prebundle",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.app.selector",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.app.setup",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.articulation_inspector",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.asset_browser",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.assets_check",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.benchmark.services",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.benchmarks",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.block_world",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.camera_inspector",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.cloner",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.common_includes",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.conveyor",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.conveyor.ui",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/isaacsim.core",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/isaacsim.core_archive",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/isaacsim.core_archive/pip_prebundle",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/isaacsim.core_nodes",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.cortex",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.cortex.sample_behaviors",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.cortex_sync",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.debug_draw",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.doctest",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.dynamic_control",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.examples",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.examples_nodes",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.extension_templates",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.franka",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.gain_tuner",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.grasp_editor",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.import_wizard",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.internal_tools",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.jupyter_notebook",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.kit",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.lula",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.lula/pip_prebundle",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.lula_test_widget",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.manipulators",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.manipulators.ui",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.menu",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.merge_mesh",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.ml_archive",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.ml_archive/pip_prebundle",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.motion_generation",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.nucleus",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.occupancy_map",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.occupancy_map.ui",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.ocs2",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.physics_inspector",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.physics_utilities",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.proximity_sensor",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.quadruped",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.range_sensor",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.range_sensor.examples",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.range_sensor.ui",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.repl",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.robot_assembler",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.robot_description_editor",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.ros2_bridge",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.ros2_bridge.robot_description",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.ros_bridge",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.scene_blox",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.sensor",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.surface_gripper",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.surface_gripper.ui",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.synthetic_recorder",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.tests",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.tf_viewer",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.throttling",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.ui",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.ui_template",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.universal_robots",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.utils",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.version",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.vscode",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.wheeled_robots",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.wheeled_robots.ui",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.isaac.window.about",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.kit.loop-isaac",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.kit.property.isaac",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.pip.cloud",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.pip.cloud/pip_prebundle",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.pip.compute",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.pip.compute/pip_prebundle",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.replicator.isaac",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/exts/omni.usd.schema.isaac",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.convexdecomposition",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.kit.property.physx",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.kvdb",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.localcache",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.physics.tensors",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.physics.tensors.tests",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.physx",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.physx.bundle",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.physx.camera",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.physx.cct",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.physx.commands",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.physx.cooking",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.physx.demos",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.physx.fabric",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.physx.forcefields",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.physx.foundation",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.physx.graph",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.physx.internal",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.physx.pvd",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.physx.stageupdate",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.physx.supportui",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.physx.telemetry",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.physx.tensors",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.physx.tests",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.physx.tests.mini",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.physx.tests.visual",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.physx.ui",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.physx.vehicle",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.physx.vehicle.tests",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.physx.zerogravity",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.usd.schema.forcefield",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.usd.schema.physx",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.usdphysics",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.usdphysics.tests",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extsPhysics/omni.usdphysics.ui",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/carb.audio-0.1.0+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/carb.imaging.python-0.1.0+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/carb.windowing.plugins-1.0.0+10a4b5c0.lx64.r",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.activity.core-1.0.1+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.anim.asset-106.1.0+106.1.0.lx64.r",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.anim.behavior.schema-106.1.0+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.anim.curve.bundle-1.2.3+106.1.0.ub3f",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.anim.curve.core-1.1.14+106.1.0.lx64.r.cp310.ub3f",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.anim.curve.ui-1.3.17+106.1.0.ub3f",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.anim.curve_editor-105.17.10+106.0.1.ub3f",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.anim.graph.bundle-106.1.0+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.anim.graph.core-106.1.2+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.anim.graph.schema-106.1.0+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.anim.graph.ui-106.1.1+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.anim.navigation.bundle-106.1.0+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.anim.navigation.core-106.1.3+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.anim.navigation.navmesh.recast-106.1.4+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.anim.navigation.schema-106.1.0+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.anim.navigation.ui-106.1.1+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.anim.people-0.5.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.anim.retarget.bundle-106.1.0+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.anim.retarget.core-106.1.2+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.anim.retarget.ui-106.1.1+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.anim.shared.core-106.0.1+106.0.1.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.anim.skelJoint-106.1.2+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.anim.timeline-105.0.23+106.1.0.lx64.r.cp310.ub3f",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.anim.window.timeline-105.13.5+106.0.1.ub3f",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.appwindow-1.1.8+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.asset_validator.core-0.11.8",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.asset_validator.ui-0.11.8",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.blobkey-1.1.2+10a4b5c0.lx64.r",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.command.usd-1.0.3+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.cuopt.examples-1.0.0+106.0.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.cuopt.service-1.0.0+106.0.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.cuopt.visualization-1.0.0+106.0.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.curve.creator-105.0.4+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.curve.manipulator-105.2.8+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.datastore-0.0.0+10a4b5c0.lx64.r",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.debugdraw-0.1.3+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.extended.materials-105.0.9",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.fabric.commands-1.1.5+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.flowusd-106.1.1+106.1.0.lx64.r.cp310.ub3f",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.genproc.core-106.1.0+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.gpu_foundation-0.0.0+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.gpu_foundation.shadercache.vulkan-1.0.0+10a4b5c0.lx64.r",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.gpucompute.plugins-0.0.0+10a4b5c0.lx64.r",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.graph-1.140.0+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.graph.action-1.102.1+106.0.3",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.graph.action_core-1.1.6+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.graph.action_nodes-1.24.0+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.graph.bundle.action-2.4.1+106.0.3",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.graph.core-2.179.2+10a4b5c0.lx64.r",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.graph.exec-0.9.4+10a4b5c0.lx64.r",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.graph.image.core-0.4.5+10a4b5c0.lx64.r",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.graph.image.nodes-1.1.0+10a4b5c0.lx64.r",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.graph.io-1.9.1+106.0.3",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.graph.nodes-1.146.1+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.graph.scriptnode-1.20.1+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.graph.telemetry-2.15.0+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.graph.tools-1.79.0+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.graph.tutorials-1.29.0+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.graph.ui-1.70.2+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.graph.ui_nodes-1.26.0+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.graph.visualization.nodes-2.1.1",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.graph.window.action-1.28.0+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.graph.window.core-1.113.1+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.graph.window.generic-1.26.0+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.hsscclient-1.1.1+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.hydra.engine.stats-1.0.2+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.hydra.iray.shadercache.vulkan-1.0.0+10a4b5c0.lx64.r",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.hydra.rtx-1.0.0+10a4b5c0.lx64.r",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.hydra.rtx.shadercache.vulkan-1.0.0+10a4b5c0.lx64.r",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.hydra.scene_api-0.1.2+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.hydra.scene_delegate-0.3.3+10a4b5c0.lx64.r",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.hydra.usdrt_delegate-7.5.1+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.importer.mjcf-1.1.1+106.0.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.importer.onshape-0.7.3+106.0.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.importer.urdf-1.14.1+106.0.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.inspect-1.0.1+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.iray.libs-0.0.0+10a4b5c0.lx64.r",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.actions.core-1.0.0+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.actions.window-1.1.1+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.agent.watcher-0.2.1",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.asset_converter-2.1.21+lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.audiodeviceenum-1.0.1+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.browser.asset-1.3.10",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.browser.asset_provider.local-1.0.9",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.browser.core-2.3.11",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.browser.deepsearch-1.1.8",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.browser.folder.core-1.9.13",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.browser.material-1.6.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.browser.sample-1.4.8",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.browser.texture-1.2.1",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.capture.viewport-1.5.1",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.clipboard-1.0.4+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.collaboration.channel_manager-1.0.12+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.collaboration.presence_layer-1.0.9+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.collaboration.telemetry-1.0.0+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.commands-1.4.9+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.context_menu-1.8.1+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.converter.cad-201.1.0+106.0.1",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.converter.common-500.0.10+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.converter.dgn-500.0.6+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.converter.dgn_core-500.0.19+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.converter.geojson-0.0.10+lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.converter.hoops-500.0.7+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.converter.hoops_core-500.0.17+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.converter.jt-500.0.7+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.converter.jt_core-500.0.16+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.converter.lib3mf-1.1.3",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.converter.ogc-1.1.22+lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.converter.stl-0.1.1+105.1",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.converter.vtk-2.3.1+105.2.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.core.collection-0.1.7",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.data2ui.core-1.0.27+106.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.data2ui.usd-1.0.27+106.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.environment.core-1.3.14",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.exec.core-0.13.4+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.gfn-106.0.5+106.0.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.graph.delegate.default-1.2.2",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.graph.delegate.modern-1.10.6",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.graph.delegate.neo-1.1.3",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.graph.editor.core-1.5.3",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.graph.editor.example-1.0.24",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.graph.usd.commands-1.3.1",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.graph.widget.variables-2.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.helper.file_utils-0.1.8+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.hotkeys.core-1.3.5+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.hotkeys.window-1.4.5+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.hydra_texture-1.3.9+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.livestream.core-3.2.0+105.2.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.livestream.core-4.3.6+106.0.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.livestream.messaging-1.1.1",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.livestream.native-4.1.0+106.0.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.livestream.webrtc-4.1.1+106.0.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.mainwindow-1.0.3+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.manipulator.camera-105.0.5+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.manipulator.prim-107.0.0+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.manipulator.prim.core-107.0.4+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.manipulator.prim.fabric-107.0.3+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.manipulator.prim.usd-107.0.2+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.manipulator.selection-106.0.1+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.manipulator.selector-1.1.1+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.manipulator.tool.mesh_snap-1.4.5+106.0.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.manipulator.tool.snap-1.5.11+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.manipulator.transform-104.7.4+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.manipulator.viewport-107.0.0+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.material.library-1.5.6+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.menu.aov-1.1.4+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.menu.common-1.1.7+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.menu.core-1.0.4+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.menu.create-1.0.16+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.menu.edit-1.1.24+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.menu.file-1.1.14+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.menu.stage-1.2.5",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.menu.utils-1.5.27+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.mesh.raycast-105.4.0+106.0.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.ngsearch-0.3.3",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.notification_manager-1.0.9+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.numpy.common-0.1.2+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.pip_archive-0.0.0+10a4b5c0.lx64.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.pip_archive-0.0.0+10a4b5c0.lx64.cp310/pip_prebundle",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.pipapi-0.0.0+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.playlist.core-1.3.4",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.pointclouds-1.4.3+cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.preferences.animation-1.1.8+106.1.0.ub3f",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.prim.icon-1.0.13",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.primitive.mesh-1.0.17+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.profiler.tracy-1.1.5+106.0.0.lx64",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.profiler.window-2.2.3",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.property.adapter.core-1.0.1+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.property.adapter.fabric-1.0.1+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.property.adapter.usd-1.0.1+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.property.audio-1.0.14+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.property.bundle-1.3.1+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.property.camera-1.0.8+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.property.collection-0.1.17",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.property.environment-1.2.1",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.property.geometry-1.3.1+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.property.layer-1.1.8+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.property.light-1.0.10+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.property.material-1.10.8+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.property.render-1.1.2+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.property.sbsar-107.0.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.property.transform-1.5.9+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.property.usd-4.2.8+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.property.visualization-104.0.5",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.quicklayout-1.0.7+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.raycast.query-1.0.5+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.renderer.capture-0.0.0+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.renderer.core-1.0.2+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.renderer.cuda_interop-1.0.1+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.renderer.imgui-1.0.1+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.renderer.init-0.0.0+10a4b5c0.lx64.r",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.scripting-106.1.1+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.search.files-1.0.4",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.search.service-0.1.12",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.search_core-1.0.5+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.selection-0.1.4+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.sequencer.core-103.4.2+106.0.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.sequencer.usd-103.4.5+106.0.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.stage.copypaste-1.2.2+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.stage.mdl_converter-1.0.6+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.stage_column.payload-2.0.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.stage_column.variant-1.0.13",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.stage_template.core-1.1.22+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.stage_templates-1.2.5+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.stagerecorder.bundle-105.0.2+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.stagerecorder.core-105.0.5+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.stagerecorder.ui-105.0.6+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.streamsdk.plugins-3.2.1+105.2.lx64.r",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.streamsdk.plugins-4.5.2+106.0.0.lx64.r",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.streamsdk.plugins-4.5.3+106.0.0.lx64.r",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.telemetry-0.5.0+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.test-1.1.0+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.test_helpers_gfx-0.0.0+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.thumbnails.mdl-1.0.24",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.thumbnails.usd-1.0.9",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.timeline.minibar-1.2.9",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.tool.asset_exporter-1.3.3",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.tool.asset_importer-2.5.5",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.tool.collect-2.2.14",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.tool.measure-105.2.6+106.0.1",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.tool.remove_unused.controller-0.1.3",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.tool.remove_unused.core-0.1.2",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.ui.actions-1.0.2+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.ui_test-1.3.2+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.uiapp-0.0.0+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.usd.collect-2.2.21+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.usd.layers-2.1.36+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.usd.mdl-1.0.1+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.usd_undo-0.1.8+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.usda_edit-1.1.14+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.variant.editor-106.1.0+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.variant.presenter-106.0.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.viewport.actions-106.0.2+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.viewport.bundle-104.0.1+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.viewport.legacy_gizmos-1.1.0+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.viewport.manipulator.transform-107.0.2+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.viewport.menubar.camera-107.0.2+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.viewport.menubar.core-106.1.0+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.viewport.menubar.display-107.0.2+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.viewport.menubar.lighting-106.0.2+ub3f",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.viewport.menubar.render-106.1.1+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.viewport.menubar.settings-107.0.3+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.viewport.menubar.waypoint-104.2.16",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.viewport.registry-104.0.6+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.viewport.rtx-104.0.1+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.viewport.utility-1.0.17+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.viewport.window-107.0.6+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.viewport_widgets_manager-1.0.8+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.waypoint.bundle-1.0.4",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.waypoint.core-1.4.54",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.waypoint.playlist-1.0.8",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.browser_bar-2.0.10+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.cache_indicator-2.0.10+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.calendar-1.0.8",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.collection-0.1.18",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.context_menu-1.2.2+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.extended_searchfield-1.0.28",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.filebrowser-2.10.51+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.filter-1.1.4+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.graph-1.12.15+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.highlight_label-1.0.2+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.imageview-1.0.3+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.inspector-1.0.3+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.layers-1.8.0+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.live-2.1.8+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.live_session_management-1.2.20+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.live_session_management.ui-1.0.1+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.material_preview-1.0.16",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.nucleus_connector-1.1.9+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.nucleus_info-1.0.2+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.opengl-1.0.8+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.options_button-1.0.3+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.options_menu-1.1.6+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.path_field-2.0.10+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.prompt-1.0.7+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.search_delegate-1.0.5+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.searchable_combobox-1.0.6+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.searchfield-1.1.8+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.settings-1.2.2+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.sliderbar-1.0.10",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.stage-2.11.2+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.stage_icons-1.0.5+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.text_editor-1.0.2+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.timeline-105.0.1+105.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.toolbar-1.7.2+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.versioning-1.4.7+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.viewport-106.1.0+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widget.zoombar-1.0.5",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.widgets.custom-1.0.8",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.window.collection-0.1.22",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.window.commands-0.2.6+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.window.console-0.2.13+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.window.content_browser-2.9.18+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.window.content_browser_registry-0.0.6+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.window.cursor-1.1.2+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.window.drop_support-1.0.3+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.window.extensions-1.4.11+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.window.file-1.3.54+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.window.file_exporter-1.0.30+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.window.file_importer-1.1.12+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.window.filepicker-2.10.40+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.window.material-1.6.2",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.window.material_graph-1.8.18",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.window.movie_capture-2.4.2",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.window.popup_dialog-2.0.24+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.window.preferences-1.6.0+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.window.property-1.11.3+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.window.quicksearch-2.4.4",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.window.script_editor-1.7.6+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.window.section-107.0.1",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.window.stage-2.5.10+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.window.stats-0.1.6+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.window.status_bar-0.1.7+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.window.title-1.1.5+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.window.toolbar-1.6.1+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.window.usd_paths-1.0.7+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.window.usddebug-1.0.2",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.xr.advertise-106.1.24+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.xr.core-106.1.24+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.xr.example.usd_scene_ui-106.0.116+106.0.1",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.xr.profile.ar-106.1.24+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.xr.profile.common-106.1.24+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.xr.profile.tabletar-106.1.24+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.xr.profile.vr-106.1.24+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.xr.scene_view.core-106.1.24+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.xr.scene_view.utils-106.1.24+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.xr.system.cloudxr-106.1.24+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.xr.system.cloudxr41-106.1.24+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.xr.system.openxr-106.1.24+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.xr.system.playback-106.1.24+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.xr.system.steamvr-106.1.24+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.xr.telemetry-106.1.24+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.xr.ui.config.common-106.1.24+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.xr.ui.config.generic-106.1.24+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.xr.ui.config.htcvive-106.1.24+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.xr.ui.config.magicleap-106.1.24+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.xr.ui.config.metaquest-106.1.24+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.xr.ui.stage.common-106.1.24+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.xr.ui.window.profile-106.1.24+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.kit.xr.ui.window.viewport-106.1.24+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.materialx.libs-1.0.4+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.mdl-55.0.1+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.mdl.neuraylib-0.2.8+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.mdl.usd_converter-1.0.21+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.net-0.0.1-isaac-1+lx64.r",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.no_code_ui.bundle-1.0.27+106.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.product_configurator.panel-1.0.15",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.product_configurator.utils-1.2.2",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.ramp-105.1.15+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.replicator.agent.camera_calibration-0.4.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.replicator.agent.core-0.4.1",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.replicator.agent.ui-0.4.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.replicator.core-1.11.20+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.replicator.object-0.3.8+lx64",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.replicator.replicator_yaml-2.0.6+lx64",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.resourcemonitor-105.0.1+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.rtx.settings.core-0.6.3+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.rtx.shadercache.vulkan-1.0.0+10a4b5c0.lx64.r",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.rtx.window.settings-0.6.17+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.scene.optimizer.bundle-106.1.1+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.scene.optimizer.core-106.1.1+106.1.0.lx64.r.cp310.ub3f",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.scene.optimizer.ui-106.1.1+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.scene.visualization.bundle-105.1.0+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.scene.visualization.core-105.4.13+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.scene.visualization.ui-105.1.2+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.sensors.nv.camera-0.10.0-isaac-1+lx64.r",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.sensors.nv.common-1.2.2-isaac-1+lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.sensors.nv.ids-1.1.0-isaac-1+lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.sensors.nv.lidar-1.2.2-isaac-1+lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.sensors.nv.materials-1.2.1-isaac-1+lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.sensors.nv.radar-1.2.1-isaac-1+lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.sensors.nv.ultrasonic-1.2.1-isaac-1+lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.sensors.nv.visualizer-1.0.1-isaac-1+lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.sensors.nv.wpm-1.2.1-isaac-1+lx64.r",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.sensors.tiled-0.0.6+106.1.0.lx64.r",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.services.browser.asset-1.3.3+106.0.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.services.carb.event_stream-1.0.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.services.client-0.5.3",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.services.core-1.9.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.services.facilities.base-1.0.4",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.services.facilities.workqueue-1.1.2",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.services.pip_archive-0.13.6+lx64",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.services.starfleet.auth-0.1.5",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.services.streamclient.webrtc-1.3.8",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.services.streamclient.websocket-2.0.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.services.streaming.manager-0.3.10",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.services.thumbnails.images-1.3.2",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.services.transport.client.base-1.2.4",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.services.transport.client.http_async-1.3.6",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.services.transport.server.base-1.1.1",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.services.transport.server.http-1.3.1",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.services.transport.server.zeroconf-1.0.9",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.services.usd-1.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.simready.explorer-1.1.1",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.slangnode-106.1.0+106.1.0.lx64.r.cp310.ub3f",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.stats-1.0.1+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.syntheticdata-0.6.9+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.timeline-1.0.10+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.timeline.live_session-1.0.8+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.tools.array-105.0.4",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.ui-2.25.22+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.ui.scene-1.10.3+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.ui_query-1.1.4+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.uiaudio-1.0.0+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.ujitso.client-0.0.0+10a4b5c0.lx64.r",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.ujitso.default-1.0.0+10a4b5c0.lx64.r",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.ujitso.processor.texture-1.0.0+10a4b5c0.lx64.r",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.usd-1.12.2+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.usd.config-1.0.4+10a4b5c0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.usd.core-1.4.1+10a4b5c0.lx64.r",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.usd.fileformat.e57-1.2.4+106.0.0.lx64.r.cp310.ub3f",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.usd.fileformat.pts-106.0.1+106.0.0.lx64.r.cp310.ub3f",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.usd.fileformat.sbsar-107.0.2+lx64.r.cp310.ub3f",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.usd.libs-1.0.1+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.usd.metrics.assembler-106.1.0+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.usd.metrics.assembler.physics-106.1.0+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.usd.metrics.assembler.ui-106.1.0+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.usd.schema.anim-0.0.0+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.usd.schema.audio-0.0.0+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.usd.schema.destruction-0.7.0+106.0.1.lx64.r",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.usd.schema.flow-106.0.8+106.0.0.ub3f",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.usd.schema.geospatial-0.0.0+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.usd.schema.metrics.assembler-106.1.0+106.1.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.usd.schema.omnigraph-1.0.0+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.usd.schema.omniscripting-1.0.0+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.usd.schema.scene.visualization-2.0.2+106.1.0",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.usd.schema.semantics-0.0.0+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.usd.schema.sequence-2.3.1+106.0.0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.usd_resolver-1.0.0+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.vdb_timesample_editor-0.1.10",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.volume-0.5.0+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.warehouse_creator-0.4.2",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.warp-1.2.1",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/omni.warp.core-1.2.1",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/semantics.schema.editor-0.3.8",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/semantics.schema.property-1.0.4",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/extscache/usdrt.scenegraph-7.5.0+10a4b5c0.lx64.r.cp310",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/kit/extscore/omni.assets.plugins",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/kit/extscore/omni.client",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/kit/extscore/omni.kit.async_engine",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/kit/extscore/omni.kit.registry.nucleus",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/kit/kernel/py",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/kit/plugins/bindings-python",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/kit/python/lib/python3.10/site-packages",
        "${workspaceFolder}/../../anaconda3/envs/QiruiItem/lib/python3.10/site-packages/isaacsim/python_packages",
        "${workspaceFolder}/exts/Qiruiitem"
    ],
    "python.analysis.typeCheckingMode": "basic"
}
