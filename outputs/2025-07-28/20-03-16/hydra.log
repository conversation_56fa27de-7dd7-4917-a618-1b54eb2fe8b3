[2025-07-28 20:03:16,507][ogn_registration][INFO] - Looking for Python nodes to register in omni.physx.fabric-106.5.7
[2025-07-28 20:03:16,507][ogn_registration][INFO] -  -> Registered nodes from module omni.physxfabric at /home/<USER>/isaacsim/extsPhysics/omni.physx.fabric
[2025-07-28 20:03:16,507][ogn_registration][INFO] - Registering nodes in /home/<USER>/isaacsim/extsPhysics/omni.physx.fabric imported as omni.physxfabric with AutoNode config {}
[2025-07-28 20:03:16,507][ogn_registration][INFO] - Registering Python Node Types from omni.physxfabric at /home/<USER>/isaacsim/extsPhysics/omni.physx.fabric in omni.physx.fabric
[2025-07-28 20:03:16,507][ogn_registration][INFO] - ========================================================================================================================
[2025-07-28 20:03:16,508][ogn_registration][INFO] - No dependency on omni.graph, therefore no nodes to register in omni.physx.fabric
[2025-07-28 20:03:16,508][ogn_registration][INFO] - ...None found, no registration to do
[2025-07-28 20:03:16,508][ogn_registration][INFO] - ...Skipping: No OmniGraph presence in the module omni.physxfabric - No nodes in this module, do not remember it
[2025-07-28 20:03:16,508][ogn_registration][INFO] - Destroying registration record for omni.physx.fabric
[2025-07-28 20:03:16,508][ogn_registration][INFO] - OGN register omni.physx.fabric-106.5.7 took 981684.000000
