训练：
	1 在/home/<USER>/QiruiItem/IsaacLabExtensionTemplate文件夹下打开终端；
	2 激活QiruiItem虚拟环境，终端输入：conda activate QiruiItem；
	3 终端输入： python scripts/rsl_rl/train.py --task=Isaac-QituiItem-Direct-v0 --enable_cameras --headless；
	
演示：
	1 在/home/<USER>/QiruiItem/IsaacLabExtensionTemplate文件夹下打开终端；
	2 激活QiruiItem虚拟环境，终端输入：conda activate QiruiItem；
	3 终端输入：  python scripts/rsl_rl/play.py --task=Isaac-QituiItem-Direct-v0 --enable_cameras --num_envs 1；
	
配置自己的网络：
	1 打开本地的虚拟环境安装路径，找到其中的相应强化学习库的位置，例如：对于rsl_rl，找到../rsl_rl/modules；
	2 复制其中的网络py文件，并命名为自己的文件，例如，复制actor_critic.py为actor_critic_qirui.py；
	3 修改py文件中的网络结构；
	4 修改modules中的_init_.py文件，添加自己网络库文件，例如，添加from .actor_critic_qirui import ActorCritic_QIRUI，__all__ = ["ActorCritic", "ActorCriticRecurrent","ActorCritic_QIRUI"]
	5 在runners文件中的on_policy_runner.py文件中导入库文件，如from rsl_rl.modules import ActorCritic, ActorCriticRecurrent, EmpiricalNormalization,ActorCritic_QIRUI，并相应修改文件中引用ActorCritic的地方；
	6 回到项目文件夹中的agents中的配置文件，修改配置。例如，修改rsl_rl_ppo_QiruiItem_cfg.py中的calss_name为"ActorCritic_QIRUI"。
	
