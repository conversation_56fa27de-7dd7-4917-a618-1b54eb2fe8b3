# Copyright (c) 2022-2025, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from isaaclab.utils import configclass

from isaaclab_rl.rsl_rl import (
    RslRlOnPolicyRunnerCfg,
    RslRlPpoActorCriticCfg,
    RslRlPpoAlgorithmCfg,
)


@configclass
class QiruiItemPPORunnerCfg(RslRlOnPolicyRunnerCfg):
    num_steps_per_env = 20
    max_iterations = 50000
    save_interval = 100
    experiment_name = "QituiItem_direct_RA2"
    empirical_normalization = False
    # resume = True

    # load_run = "2025-05-16_20-31-18"
    # load_checkpoint = "best1.pt"

    policy = RslRlPpoActorCriticCfg(
        class_name = "ActorCritic_QIRUI_DT",
        init_noise_std=0.5,
        actor_hidden_dims=[512, 512, 512,512,512],
        critic_hidden_dims=[512, 512, 512,512,512],
        activation="elu",
    )
    algorithm = RslRlPpoAlgorithmCfg(
        value_loss_coef=1.0,
        use_clipped_value_loss=True,
        clip_param=0.2,
        entropy_coef=0.005,
        num_learning_epochs=5,
        num_mini_batches=10,
        learning_rate=2.0e-4,
        schedule="adaptive",
        gamma=0.99,
        lam=0.95,
        desired_kl=0.01,
        max_grad_norm=1.0,
    )
