viewer:
  eye: !!python/tuple
  - 7.5
  - 7.5
  - 7.5
  lookat: !!python/tuple
  - 0.0
  - 0.0
  - 0.0
  cam_prim_path: /OmniverseKit_Persp
  resolution: !!python/tuple
  - 1280
  - 720
  origin_type: world
  env_index: 0
  asset_name: null
  body_name: null
sim:
  physics_prim_path: /physicsScene
  device: cuda:0
  dt: 0.006666666666666667
  render_interval: 4
  gravity: !!python/tuple
  - 0.0
  - 0.0
  - -9.81
  enable_scene_query_support: false
  use_fabric: true
  physx:
    solver_type: 1
    min_position_iteration_count: 1
    max_position_iteration_count: 255
    min_velocity_iteration_count: 0
    max_velocity_iteration_count: 255
    enable_ccd: false
    enable_stabilization: false
    enable_enhanced_determinism: false
    bounce_threshold_velocity: 0.5
    friction_offset_threshold: 0.04
    friction_correlation_distance: 0.025
    gpu_max_rigid_contact_count: 8388608
    gpu_max_rigid_patch_count: 163840
    gpu_found_lost_pairs_capacity: 2097152
    gpu_found_lost_aggregate_pairs_capacity: 33554432
    gpu_total_aggregate_pairs_capacity: 2097152
    gpu_collision_stack_size: 67108864
    gpu_heap_capacity: 67108864
    gpu_temp_buffer_capacity: 16777216
    gpu_max_num_partitions: 8
    gpu_max_soft_body_contacts: 1048576
    gpu_max_particle_contacts: 1048576
  physics_material:
    func: isaaclab.sim.spawners.materials.physics_materials:spawn_rigid_body_material
    static_friction: 1.0
    dynamic_friction: 1.0
    restitution: 0.0
    improve_patch_friction: true
    friction_combine_mode: multiply
    restitution_combine_mode: multiply
    compliant_contact_stiffness: 0.0
    compliant_contact_damping: 0.0
  render:
    enable_translucency: null
    enable_reflections: null
    enable_global_illumination: null
    antialiasing_mode: null
    enable_dlssg: null
    enable_dl_denoiser: null
    dlss_mode: null
    enable_direct_lighting: null
    samples_per_pixel: null
    enable_shadows: null
    enable_ambient_occlusion: null
    carb_settings: null
    rendering_mode: null
ui_window_class_type: isaaclab.envs.ui.base_env_window:BaseEnvWindow
seed: 42
decimation: 4
is_finite_horizon: false
episode_length_s: 10.0
scene:
  num_envs: 15
  env_spacing: 3
  lazy_sensor_update: true
  replicate_physics: true
  filter_collisions: true
events: null
observation_space: 262158
num_observations: null
state_space: 0
num_states: null
observation_noise_model: null
action_space: 14
num_actions: null
action_noise_model: null
rerender_on_reset: false
wait_for_textures: true
xr: null
debug_vis: true
Qiruirobot:
  class_type: isaaclab.assets.articulation.articulation:Articulation
  prim_path: /World/envs/env_.*/QiruiRobot
  spawn:
    func: isaaclab.sim.spawners.from_files.from_files:spawn_from_usd
    visible: true
    semantic_tags: null
    copy_from_source: true
    mass_props: null
    deformable_props: null
    rigid_props:
      rigid_body_enabled: true
      kinematic_enabled: null
      disable_gravity: true
      linear_damping: null
      angular_damping: null
      max_linear_velocity: null
      max_angular_velocity: null
      max_depenetration_velocity: 10.0
      max_contact_impulse: 1.0e+32
      enable_gyroscopic_forces: false
      retain_accelerations: false
      solver_position_iteration_count: null
      solver_velocity_iteration_count: null
      sleep_threshold: null
      stabilization_threshold: null
    collision_props: null
    activate_contact_sensors: true
    scale: null
    articulation_props:
      articulation_enabled: null
      enabled_self_collisions: true
      solver_position_iteration_count: 8
      solver_velocity_iteration_count: 0
      sleep_threshold: 0.005
      stabilization_threshold: 0.0005
      fix_root_link: null
    fixed_tendons_props: null
    joint_drive_props: null
    visual_material_path: material
    visual_material: null
    usd_path: USDs/QiruiRobot.usd
    variants: null
  init_state:
    pos: !!python/tuple
    - 0
    - 0.75
    - 1.0
    rot:
    - 0.7071
    - 0.0
    - 0.0
    - -0.7071
    lin_vel: !!python/tuple
    - 0.0
    - 0.0
    - 0.0
    ang_vel: !!python/tuple
    - 0.0
    - 0.0
    - 0.0
    joint_pos:
      .*_(left|right)_arm_joint1: 0.6
      .*_left_arm_joint2: 0.44
      .*_right_arm_joint2: -0.44
      .*_(left|right)_arm_joint3: 0.41
      .*_left_arm_joint4: -1.07
      .*_right_arm_joint4: 1.07
      .*_(left|right)_arm_joint5: 0.4
      .*_(left|right)_arm_joint6: 0.0
      .*_(left|right)_arm_joint7: 0.0
      (left|right)_hand_thumb_proximal_yaw_joint: 1.3
    joint_vel:
      .*: 0.0
  collision_group: 0
  debug_vis: false
  articulation_root_prim_path: null
  soft_joint_pos_limit_factor: 1.0
  actuators:
    Arms:
      class_type: isaaclab.actuators.actuator_pd:ImplicitActuator
      joint_names_expr:
      - .*_(left|right)_arm_joint[1-7]
      effort_limit:
        .*_(left|right)_arm_joint[1-2]: 100000.0
        .*_(left|right)_arm_joint[3-4]: 100000.0
        .*_(left|right)_arm_joint[5-7]: 100000.0
      velocity_limit: 100.0
      effort_limit_sim: null
      velocity_limit_sim: null
      stiffness:
        .*_(left|right)_arm_joint[1-4]: 10000000.0
        .*_(left|right)_arm_joint[5-7]: 10000000.0
      damping:
        .*_(left|right)_arm_joint[1-4]: 100.0
        .*_(left|right)_arm_joint[5-7]: 100.0
      armature: null
      friction: 0.1
    Hands:
      class_type: isaaclab.actuators.actuator_pd:ImplicitActuator
      joint_names_expr:
      - (left|right)_hand_(index|middle|pinky|ring)_(proximal|intermediate)_joint
      - (left|right)_hand_thumb_(proximal_(pitch|yaw)|intermediate|distal)_joint
      effort_limit: 3.0
      velocity_limit: 20.0
      effort_limit_sim: null
      velocity_limit_sim: null
      stiffness:
        (left|right)_hand_(index|middle|pinky|ring)_(proximal|intermediate)_joint: 50.0
        (left|right)_hand_thumb_(proximal_pitch|intermediate|distal)_joint: 30.0
        (left|right)_hand_thumb_proximal_yaw_joint: 30.0
      damping:
        (left|right)_hand_(index|middle|pinky|ring)_(proximal|intermediate)_joint: 20
        (left|right)_hand_thumb_(proximal_pitch|intermediate|distal)_joint: 20
        (left|right)_hand_thumb_proximal_yaw_joint: 20
      armature: null
      friction: 1.3
Kettle:
  class_type: isaaclab.assets.rigid_object.rigid_object:RigidObject
  prim_path: /World/envs/env_.*/Kettle
  spawn:
    func: isaaclab.sim.spawners.from_files.from_files:spawn_from_usd
    visible: true
    semantic_tags: null
    copy_from_source: true
    mass_props:
      mass: 1.0
      density: null
    deformable_props: null
    rigid_props:
      rigid_body_enabled: null
      kinematic_enabled: null
      disable_gravity: null
      linear_damping: null
      angular_damping: null
      max_linear_velocity: null
      max_angular_velocity: null
      max_depenetration_velocity: null
      max_contact_impulse: null
      enable_gyroscopic_forces: null
      retain_accelerations: null
      solver_position_iteration_count: null
      solver_velocity_iteration_count: null
      sleep_threshold: null
      stabilization_threshold: null
    collision_props:
      collision_enabled: null
      contact_offset: null
      rest_offset: null
      torsional_patch_radius: null
      min_torsional_patch_radius: null
    activate_contact_sensors: true
    scale: null
    articulation_props: null
    fixed_tendons_props: null
    joint_drive_props: null
    visual_material_path: material
    visual_material: null
    usd_path: USDs/bottle.usd
    variants: null
  init_state:
    pos:
    - -0.16
    - 0.27
    - 1.11
    rot:
    - 0.7071
    - 0.7071
    - 0.0
    - 0.0
    lin_vel: !!python/tuple
    - 0.0
    - 0.0
    - 0.0
    ang_vel: !!python/tuple
    - 0.0
    - 0.0
    - 0.0
  collision_group: 0
  debug_vis: false
Cup:
  class_type: isaaclab.assets.rigid_object.rigid_object:RigidObject
  prim_path: /World/envs/env_.*/Cup
  spawn:
    func: isaaclab.sim.spawners.from_files.from_files:spawn_from_usd
    visible: true
    semantic_tags: null
    copy_from_source: true
    mass_props:
      mass: 0.5
      density: null
    deformable_props: null
    rigid_props:
      rigid_body_enabled: null
      kinematic_enabled: null
      disable_gravity: null
      linear_damping: null
      angular_damping: null
      max_linear_velocity: null
      max_angular_velocity: null
      max_depenetration_velocity: null
      max_contact_impulse: null
      enable_gyroscopic_forces: null
      retain_accelerations: null
      solver_position_iteration_count: null
      solver_velocity_iteration_count: null
      sleep_threshold: null
      stabilization_threshold: null
    collision_props:
      collision_enabled: null
      contact_offset: null
      rest_offset: null
      torsional_patch_radius: null
      min_torsional_patch_radius: null
    activate_contact_sensors: true
    scale: null
    articulation_props: null
    fixed_tendons_props: null
    joint_drive_props: null
    visual_material_path: material
    visual_material: null
    usd_path: USDs/cup4.usd
    variants: null
  init_state:
    pos:
    - 0.16
    - 0.27
    - 1.13
    rot:
    - 0
    - 0
    - 0.7071
    - -0.7071
    lin_vel: !!python/tuple
    - 0.0
    - 0.0
    - 0.0
    ang_vel: !!python/tuple
    - 0.0
    - 0.0
    - 0.0
  collision_group: 0
  debug_vis: false
camera:
  class_type: isaaclab.sensors.camera.tiled_camera:TiledCamera
  prim_path: /World/envs/env_.*/QiruiRobot/front_cam
  update_period: 0.1
  history_length: 0
  debug_vis: true
  offset:
    pos: !!python/tuple
    - 0.06
    - 0.0
    - 0.3527
    rot:
    - -0.40558
    - 0.57923
    - -0.57923
    - 0.40558
    convention: ros
  spawn:
    func: isaaclab.sim.spawners.sensors.sensors:spawn_camera
    visible: true
    semantic_tags: null
    copy_from_source: true
    projection_type: pinhole
    clipping_range: !!python/tuple
    - 0.1
    - 100000.0
    focal_length: 24.0
    focus_distance: 400.0
    f_stop: 0.0
    horizontal_aperture: 30
    vertical_aperture: null
    horizontal_aperture_offset: 0.0
    vertical_aperture_offset: 0.0
    lock_camera: true
  depth_clipping_behavior: none
  data_types:
  - rgb
  - depth
  width: 320
  height: 180
  update_latest_camera_pose: false
  semantic_filter: '*:*'
  colorize_semantic_segmentation: true
  colorize_instance_id_segmentation: true
  colorize_instance_segmentation: true
  semantic_segmentation_mapping: {}
Table:
  class_type: isaaclab.assets.rigid_object.rigid_object:RigidObject
  prim_path: /World/envs/env_.*/Table
  spawn:
    func: isaaclab.sim.spawners.from_files.from_files:spawn_from_usd
    visible: true
    semantic_tags: null
    copy_from_source: true
    mass_props:
      mass: 10000.0
      density: null
    deformable_props: null
    rigid_props: null
    collision_props:
      collision_enabled: null
      contact_offset: null
      rest_offset: null
      torsional_patch_radius: null
      min_torsional_patch_radius: null
    activate_contact_sensors: true
    scale: null
    articulation_props: null
    fixed_tendons_props: null
    joint_drive_props: null
    visual_material_path: material
    visual_material: null
    usd_path: USDs/table.usd
    variants: null
  init_state:
    pos:
    - 0.0
    - 0.5
    - 0.55
    rot:
    - 1.0
    - 0.0
    - 0.0
    - 0.0
    lin_vel: !!python/tuple
    - 0.0
    - 0.0
    - 0.0
    ang_vel: !!python/tuple
    - 0.0
    - 0.0
    - 0.0
  collision_group: 0
  debug_vis: false
action_scale: 0.5
