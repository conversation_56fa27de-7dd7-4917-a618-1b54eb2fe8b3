seed: 42
device: cuda:0
num_steps_per_env: 128
max_iterations: 10
empirical_normalization: false
policy:
  class_name: ActorCritic_QIRUI
  init_noise_std: 0.5
  noise_std_type: scalar
  actor_hidden_dims:
  - 256
  - 256
  critic_hidden_dims:
  - 256
  - 256
  activation: elu
algorithm:
  class_name: PPO
  num_learning_epochs: 8
  num_mini_batches: 40
  learning_rate: 0.0002
  schedule: adaptive
  gamma: 0.99
  lam: 0.95
  entropy_coef: 0.005
  desired_kl: 0.01
  max_grad_norm: 1.0
  value_loss_coef: 1.0
  use_clipped_value_loss: true
  clip_param: 0.2
  normalize_advantage_per_mini_batch: false
  symmetry_cfg: null
  rnd_cfg: null
clip_actions: null
save_interval: 100
experiment_name: QituiItem_direct_Depth
run_name: ''
logger: tensorboard
neptune_project: isaaclab
wandb_project: isaaclab
resume: false
load_run: .*
load_checkpoint: model_.*.pt
