
# Copyright (c) 2022-2025, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause
#==============================================================================未使用逆运动学知识=============================================================================================
#==============================================================================考虑水壶和水杯位置的随机性=======================================================================================
#====================================================================================全程训练======================================================================================
from __future__ import annotations
from PIL import Image
import torch
torch.cuda.empty_cache()
# import PyKDL as kdl
# from ultralytics import YOLO
import torchvision.transforms as transforms
# from urdf_parser_py.urdf import URDF
# from pykdl_utils.kdl_parser import kdl_tree_from_urdf_model
import numpy as np
from isaacsim.core.utils.stage import get_current_stage
from isaacsim.core.utils.torch.transformations import tf_combine, tf_inverse, tf_vector
from pxr import UsdGeom
# from omni.isaac.debug_draw import _debug_draw
# from torch.utils.tensorboard import SummaryWriter as TensorboardSummaryWriter
import isaaclab.sim as sim_utils
from isaaclab.actuators.actuator_cfg import ImplicitActuatorCfg
from isaaclab.assets import Articulation, ArticulationCfg, AssetBaseCfg, AssetBase, RigidObjectCfg, RigidObject
from isaaclab.envs import DirectRLEnv, DirectRLEnvCfg
from isaaclab.scene import InteractiveSceneCfg
from isaaclab.sim import SimulationCfg
from isaaclab.terrains import TerrainImporterCfg
from isaaclab.utils import configclass
from isaaclab.utils.assets import ISAAC_NUCLEUS_DIR
from isaaclab.utils.math import sample_uniform
from isaaclab.sensors import TiledCameraCfg,TiledCamera,ContactSensorCfg, ContactSensor
import isaaclab.utils.math as math_utils
import math
from isaaclab.utils.math import sample_uniform
from pytorch3d.ops import sample_farthest_points
import torch.nn.functional as F
import cv2

from .ObjectCfg import Table_CFG, Kettle_CFG, Cup_CFG, QiruiRobot_CFG, Camera_CFG
from copy import deepcopy
import time
import gc
# 环境配置类
@configclass
class QiruiEnvCfg(DirectRLEnvCfg):
    # Environment Configuration
    episode_length_s: float = 10.0    # 迭代长度
    decimation: int = 6          
    action_space: int = 7         #  动作空间大小：双臂运动
    observation_space: int = (3+3 + 7*3)*2  # 观测空间大小：图像（RGBD） + 手臂的关节角度值 + 手臂的关节速度值  + 手臂的末端位置 + Flag指标数
    state_space: int = 0  # Corrected to 0 to avoid negative dimensions
    debug_vis: bool = True         # 是否可显示

    # Simulation Configuration
    sim: SimulationCfg = SimulationCfg(
        dt=1 / 150,
        render_interval=decimation,
        #disable_contact_processing=True,
        physics_material=sim_utils.RigidBodyMaterialCfg(
            friction_combine_mode="multiply",
            restitution_combine_mode="multiply",
            static_friction=1.0,
            dynamic_friction=1.0,
            restitution=0.0,
        ),
    )

    # Scene Configuration
    scene: InteractiveSceneCfg = InteractiveSceneCfg(num_envs=15, env_spacing=3, replicate_physics=True)  

    # Robot Configuration

    # articulation
    Qiruirobot: ArticulationCfg = QiruiRobot_CFG.replace(prim_path="/World/envs/env_.*/QiruiRobot")   # 机器人配置

    Table = Table_CFG.replace(prim_path="/World/envs/env_.*/Table")   # 桌子配置
 
    Kettle: RigidObjectCfg = Kettle_CFG.replace(prim_path="/World/envs/env_.*/Kettle")    # 水壶配置

    Cup: RigidObjectCfg = Cup_CFG.replace(prim_path="/World/envs/env_.*/Cup")      # 水杯配置

    camera: TiledCameraCfg = Camera_CFG.replace(prim_path="/World/envs/env_.*/QiruiRobot/front_cam")     # 相机配置
    
    body_names_cup = ['left_hand_index_intermediate', 'left_hand_middle_intermediate', 'left_hand_pinky_intermediate', 'left_hand_ring_intermediate', 'left_hand_thumb_intermediate']

    for i in range(len(body_names_cup)):
        body_names_cup[i] = "/World/envs/env_.*/QiruiRobot/" + body_names_cup[i]

    contact_forces_left_hand_intermediate = ContactSensorCfg(
    prim_path="/World/envs/env_.*/Cup/cup", update_period=0.0, history_length=6, debug_vis=False, filter_prim_paths_expr=body_names_cup, 
    )   # 接触传感器配置（左手）

    body_names_kettle = ['right_hand_index_intermediate', 'right_hand_middle_intermediate', 'right_hand_pinky_intermediate', 'right_hand_ring_intermediate', 'right_hand_thumb_intermediate']

    for i in range(len(body_names_kettle)):
        body_names_kettle[i] = "/World/envs/env_.*/QiruiRobot/" + body_names_kettle[i]

    contact_forces_right_hand_intermediate = ContactSensorCfg(
        prim_path="/World/envs/env_.*/Kettle/bottle", update_period=0.0, history_length=6, debug_vis=False, filter_prim_paths_expr=body_names_kettle,
    )   # 接触传感器配置（右手）

    # contact_sensor_table: ContactSensorCfg = ContactSensorCfg(
    #     prim_path=f"/World/envs/env_.*/Table/table", update_period=0.0, history_length=6, debug_vis=True, filter_prim_paths_expr= "/World/envs/env_.*/QiruiRobot/.*(hand|wrist|arm).*",
    # )
    body_names = ['left_arm_link01', 'right_arm_link01', 'left_arm_link02', 'right_arm_link02', 'left_arm_link03', 'right_arm_link03', 'left_arm_link04', 'right_arm_link04', 'left_arm_link05', 'right_arm_link05', 'left_arm_link06', 'left_wrist_motor_A', 'left_wrist_motor_B', 'right_arm_link06', 'right_wrist_motor_A', 'right_wrist_motor_B', 'left_arm_link07', 'left_wrist_rod_A', 'left_wrist_rod_B', 'right_arm_link07', 'right_wrist_rod_A', 'right_wrist_rod_B', 'left_hand_base', 'right_hand_base', 'left_hand_base_link', 'right_hand_base_link', 'left_hand_index_proximal', 'left_hand_middle_proximal', 'left_hand_pinky_proximal', 'left_hand_ring_proximal', 'left_hand_thumb_proximal_base', 'right_hand_index_proximal', 'right_hand_middle_proximal', 'right_hand_pinky_proximal', 'right_hand_ring_proximal', 'right_hand_thumb_proximal_base', 'left_hand_index_intermediate', 'left_hand_middle_intermediate', 'left_hand_pinky_intermediate', 'left_hand_ring_intermediate', 'left_hand_thumb_proximal', 'right_hand_index_intermediate', 'right_hand_middle_intermediate', 'right_hand_pinky_intermediate', 'right_hand_ring_intermediate', 'right_hand_thumb_proximal', 'left_hand_index_tip', 'left_hand_middle_tip', 'left_hand_pinky_tip', 'left_hand_ring_tip', 'left_hand_thumb_intermediate', 'right_hand_index_tip', 'right_hand_middle_tip', 'right_hand_pinky_tip', 'right_hand_ring_tip', 'right_hand_thumb_intermediate', 'left_hand_thumb_distal', 'right_hand_thumb_distal', 'left_hand_thumb_tip', 'right_hand_thumb_tip']

    for i in range(len(body_names)):
        body_names[i] = "/World/envs/env_.*/QiruiRobot/" + body_names[i]

    contact_sensor_table: ContactSensorCfg = ContactSensorCfg(
        prim_path="/World/envs/env_.*/Table/table", update_period=0.0, history_length=6, debug_vis=False, filter_prim_paths_expr= body_names,
    )

    action_scale = 0.01   # 动作的最大量

    # thrust_scale: float = 10.0  
    # angle_limit: float = math.pi / 4  

    # Reward Scales
    # ang_vel_reward_scale: float = -0.05
    # distance_to_goal_reward_scale: float = -0.1
    # orientation_penalty_scale: float = -0.1
    # joint_limit_penalty_scale: float = -1

class QiruiEnv(DirectRLEnv):
    # pre-physics step calls
    #   |-- _pre_physics_step(action)
    #   |-- _apply_action()
    # post-physics step calls
    #   |-- _get_dones()
    #   |-- _get_rewards()
    #   |-- _reset_idx(env_ids)
    #   |-- _get_observations()

    cfg: QiruiEnvCfg

    def __init__(self, cfg: QiruiEnvCfg, render_mode: str | None = None, **kwargs):
        super().__init__(cfg, render_mode, **kwargs)
        self.robot_root_pos = self._robot.data.root_pos_w
        self.left_arm_indices = self._robot.find_joints('.*left_arm_joint.*')[0]
        print(self._robot.find_joints('.*left_arm_joint.*'))
        # self.left_arm_indices = [2, 6, 10, 14, 18, 22, 24]
        self.right_arm_indices = self._robot.find_joints('.*right_arm_joint.*')[0]
        print(self._robot.find_joints('.*right_arm_joint.*'))
        # self.right_arm_indices = [3, 7, 11, 15, 19, 23, 25]
        self.left_hand_indices = self._robot.find_joints('left_hand.*joint')[0]
        print(self._robot.find_joints('left_hand.*joint'))
        # self.left_hand_indices = list(range(26, 31)) + list(range(36, 41)) + [46, 48]
        self.right_hand_indices = self._robot.find_joints('right_hand.*joint')[0]
        # self.right_hand_indices = list(range(31, 36)) + list(range(41, 46)) + [47, 49]

        self.camera_original_pos = self._camera.data.pos_w.clone()

        self.kettle_orinal_pos = self._kettle.data.root_pos_w.clone()    # 水杯初始位置
        self.kettle_orinal_quat = self._kettle.data.root_quat_w.clone()   # 水杯初始姿态
        self.kettle_change_pos = self._kettle.data.root_pos_w.clone()    # 水杯位置

        self.cup_orinal_pos = self._cup.data.root_pos_w.clone()    # 水壶初始位置
        self.cup_orinal_quat = self._cup.data.root_quat_w.clone()  # 水壶初始姿态
        self.cup_change_pos = self._cup.data.root_pos_w.clone()    # 水壶初始位置

        kettle_pos = self._kettle.data.root_com_pos_w.clone()      # 水壶中心位置
        self.kettle_z = kettle_pos[:,2].clone()

        self.table_orinal_pos = self._table.data.root_pos_w.clone()    # 桌子初始位置
        self.table_orinal_quat = self._table.data.root_quat_w.clone()  # 桌子初始姿态
         
        self.left_link7_idx = self._robot.find_bodies("left_arm_link07")[0][0]    # 左臂末端的索引
        self.right_link7_idx = self._robot.find_bodies("right_arm_link07")[0][0]   # 右臂末端的索引

        self.right_hand_proximal_indices = [48, 34, 35, 37, 36]
        self.left_hand_proximal_indices = [43, 29, 30, 32, 31] # 左手手指指腹的索引

        self.origi_left_link7_state  = self._robot.data.body_link_state_w[:,self.left_link7_idx,0:7]   # 左臂第7关节值
        self.origi_right_link7_state  = self._robot.data.body_link_state_w[:,self.right_link7_idx,0:7]   # 左臂第7关节值

        # 左右手指的原始位姿
        self.origi_left_hand_proximal_state = self._robot.data.body_link_state_w[:,self.left_hand_proximal_indices,0:7].clone()  
        self.origi_right_hand_proximal_state = self._robot.data.body_link_state_w[:,self.right_hand_proximal_indices,0:7].clone()

        self.origi_left_joint  = self._robot.data.joint_pos[:,self.left_arm_indices].clone()   # 关节变量
        self.origi_right_joint  = self._robot.data.joint_pos[:,self.right_arm_indices].clone()   # 关节变量

        self.pre_left_joint_p  = self._robot.data.joint_pos[:,self.left_arm_indices].clone()   # 上一个关节位置
        self.pre_right_joint_p  = self._robot.data.joint_pos[:,self.right_arm_indices].clone()   # 上一个关节位置

        self.pre_left_joint_v  = torch.zeros((self.num_envs,7),device=self.device)  # 上一个关节速度
        self.pre_right_joint_v  = torch.zeros((self.num_envs,7),device=self.device)  # 上一个关节速度

        self.detect_kettle_cup_move_t = torch.zeros((self.num_envs),device=self.device)

        self.target_decect_data = torch.zeros([self.num_envs, 800*6], device=self.device)  # 
        self.image_data_revise = {}

        self.get_image_t = 0 

        intrinsics = self._camera.data.intrinsic_matrices
        self.fx, self.fy = intrinsics[0, 0, 0], intrinsics[0, 1, 1]
        self.cx, self.cy = intrinsics[0, 0, 2], intrinsics[0, 1, 2]
        # print('self.intrinsics  ',self.intrinsics)


        # self.yolo_model = YOLO(r'/home/<USER>/ultralytics/runs/train/exp7/weights/best.pt').to(self.device)

        self.action_scale = self.cfg.action_scale    # 动作尺度
        self.robot_dof_lower_limits = self._robot.data.soft_joint_pos_limits[0, :, 0].to(device=self.device)  # 机器人关节限制最小值
        self.robot_dof_upper_limits = self._robot.data.soft_joint_pos_limits[0, :, 1].to(device=self.device)   # 机器人关节限制最大值

        # print('default_joint_pos',self._robot.data.default_joint_pos[0])
        self.robot_dof_targets = torch.zeros((self.num_envs, self._robot.num_joints), device=self.device)     # 机器人关节原始位置
        # 关节索引
        print(self._robot.find_joints('.*'))

        # 手握紧各关节值
        self.grasp_hand_upper = self.robot_dof_upper_limits[self.left_hand_indices]
        self.grasp_hand_lower = self.robot_dof_lower_limits[self.left_hand_indices]
        self.grasp_hand_base = (self.grasp_hand_upper - self.grasp_hand_lower)/100.0
        self.grasp_intent = torch.zeros((self.num_envs), device=self.device)
        print('robot_dof_lower_limits', self.robot_dof_lower_limits[self.left_hand_indices])
        print('robot_dof_upper_limits',self.robot_dof_upper_limits[self.left_hand_indices])

        # self.grasp_size = torch.zeros([self.num_envs,2], dtype=torch.float, device=self.device)

        self.flag = torch.tensor([0]*self.num_envs,device=self.device)

        self.threhold_curi = 0.09
        self.get_threhold_curi_target = torch.zeros(self.num_envs,device=self.device)
        # self.flag_1to2 = torch.tensor([False]*self.num_envs,device=self.device)
        # self.curricul_term = torch.tensor([False]*self.num_envs,device=self.device)
        self.actions_ago = torch.zeros([self.num_envs, self.cfg.action_space], device=self.device)

        # self.draw = _debug_draw.acquire_debug_draw_interface()

        # import pyvista as pv
        # points = self.target_decect_data[0].cpu().numpy()
        # # 创建 PyVista 点云对象
        # self.cloud = pv.PolyData(points)
        # # 可视化
        # self.plotter = pv.Plotter()
        # self.plotter.add_points(self.cloud, color='blue', point_size=5)
        # # 添加带有坐标轴刻度的边界框
        # self.plotter.add_bounding_box(color='black')
        # self.plotter.show_grid(
        #     grid=True,            # 显示网格
        #     xlabel='X Axis',      # X 轴标签
        #     ylabel='Y Axis',      # Y 轴标签
        #     zlabel='Z Axis',      # Z 轴标签
        #     ticks='outside',      # 刻度位置
        #     font_size=16,         # 字体大小
        #     color='black'         # 颜色
        # )
        # self.plotter.show(auto_close=False)



    # 建立场景
    def _setup_scene(self):
        self._robot = Articulation(self.cfg.Qiruirobot)
        self._kettle = RigidObject(self.cfg.Kettle)
        self._table = RigidObject(self.cfg.Table)
        self._cup = RigidObject(self.cfg.Cup)
        self._camera = TiledCamera(self.cfg.camera)
        self._contact_forces_left_hand_intermediate = ContactSensor(self.cfg.contact_forces_left_hand_intermediate)
        self._contact_forces_right_hand_intermediate= ContactSensor(self.cfg.contact_forces_right_hand_intermediate)
        self._contact_sensor_table = ContactSensor(self.cfg.contact_sensor_table)
        # self.scene.
        self.scene.articulations["robot"] = self._robot
        self.scene.rigid_objects["kettle"] = self._kettle
        self.scene.rigid_objects["table"] = self._table
        self.scene.rigid_objects["cup"] = self._cup
        self.scene.sensors["camera"] = self._camera
        self.scene.sensors["contact_forces_left_hand_intermediate"] = self._contact_forces_left_hand_intermediate
        self.scene.sensors["contact_forces_right_hand_intermediate"] = self._contact_forces_right_hand_intermediate
        self.scene.sensors["contact_sensor_table"] = self._contact_sensor_table

        # clone, filter, and replicate
        self.scene.clone_environments(copy_from_source=False)
        self.scene.filter_collisions(global_prim_paths=[])

        # add lights
        light_cfg = sim_utils.DomeLightCfg(intensity=2000.0, color=(0.75, 0.75, 0.75))
        light_cfg.func("/World/Light", light_cfg)

        cfg_ground = sim_utils.GroundPlaneCfg(size=(2000,2000))
        cfg_ground.func("/World/GroundPlane", cfg_ground)

    # 求第六和第七个关节的极限
    def limit_six_seven(self, joint_pos_67):
        joint_pos_6_c = joint_pos_67[:,0].clone()
        joint_pos_7_c = joint_pos_67[:,1].clone()
        joint_pos_6_c_abs = torch.abs(joint_pos_6_c)
        joint_pos_7_c_abs = torch.abs(joint_pos_7_c)
        fuhao_6 = joint_pos_6_c < 0
        fuhao_7 = joint_pos_7_c < 0
        # a = 2
        # b = 3
        # c = -np.pi/2
        a = 0.59
        b = 0.80
        c = -0.46
        limit_index = ((joint_pos_6_c_abs * a + joint_pos_7_c_abs * b) > -c)
        # print('limit_index ',limit_index)
        x0 = joint_pos_6_c_abs[limit_index].clone()
        y0 = joint_pos_7_c_abs[limit_index].clone()
        joint_pos_6_c_abs[limit_index] = (b**2 * x0 - a * b * y0 - a * c) / (a**2 + b**2)
        joint_pos_7_c_abs[limit_index]  = (a**2 * y0 - a * b * x0 - b * c) / (a**2 + b**2)
        joint_pos_6_c_abs[fuhao_6] = -1 * joint_pos_6_c_abs[fuhao_6]
        joint_pos_7_c_abs[fuhao_7] = -1 * joint_pos_7_c_abs[fuhao_7]
        return torch.cat((joint_pos_6_c_abs.unsqueeze(1), joint_pos_7_c_abs.unsqueeze(1)), dim=1)
    
    # pre-physics step calls
    # 手执行抓取和放开动作
    def _grasp(self, left_grasp_enable, right_grasp_enable):
        # #手张开
        # joint_lhand_loose = self._robot.data.soft_joint_pos_limits[:, self.left_hand_indices, 0]
        # self.robot_dof_targets[:, self.left_hand_indices] = joint_lhand_loose.clone()
        # joint_rhand_loose = self._robot.data.soft_joint_pos_limits[:, self.right_hand_indices, 0]
        # self.robot_dof_targets[:, self.right_hand_indices] = joint_rhand_loose.clone()
        self.robot_dof_targets[(~left_grasp_enable).nonzero(), self.left_hand_indices] = self.grasp_hand_lower
        self.robot_dof_targets[(~right_grasp_enable).nonzero(), self.right_hand_indices] = self.grasp_hand_lower

        #左手抓握
        self.robot_dof_targets[left_grasp_enable.nonzero(), self.left_hand_indices] = self.grasp_hand_upper * 0.35
        #右手抓握
        self.robot_dof_targets[right_grasp_enable.nonzero(), self.right_hand_indices] = self.grasp_hand_upper * 0.6

    # 动作前的函数
    def _pre_physics_step(self, actions: torch.Tensor):
        # 复位手的位置
        # left_grasp_enable = torch.zeros((self.num_envs), device=self.device, dtype=torch.bool)   
        # right_grasp_enable = torch.zeros((self.num_envs), device=self.device, dtype=torch.bool)
        # self._grasp(left_grasp_enable, right_grasp_enable)

        # term_left_hand = torch.flatten(self.c_l_d < 0.1)
        # term_right_hand = torch.flatten(self.k_r_d < 0.1)
        # self._grasp(term_left_hand, term_right_hand)  # 如果到达指定位置（距离小于0.1）则手抓紧、

        # 左臂右臂关节值赋值
        self.actions = actions.clone().clamp(-1.0, 1.0)
        # self.actions[((self.flag==2) | (self.flag==3)).nonzero(),[1,3]] = self.actions[((self.flag==2) | (self.flag==3)).nonzero(),[1,3]] * -1
        # print('self.actions',self.actions)
        self.robot_dof_targets = self._robot.data.joint_pos.clone()
        # print('self.actions[:,-1]',self.actions[:,-1])
        # self._grasp(self.actions[:,-1])
        # self._grasp((self.flag==4) | (self.flag==5),(self.flag==1) | (self.flag==2))
        # self.robot_dof_targets[:, self.left_hand_indices] = self.robot_dof_targets[:,self.left_hand_indices] + self.actions[:,14:15] * self.grasp_hand_base
        # self.robot_dof_targets[:, self.right_hand_indices] = self.robot_dof_targets[:,self.right_hand_indices] + self.actions[:,15:16] * self.grasp_hand_base
        arm_indices = torch.zeros((self.num_envs, 7),dtype=torch.int, device=self.device)
        arm_indices[((self.flag==0) | (self.flag==1)),:] = torch.tensor(self.right_arm_indices,dtype=torch.int,device=self.device)
        arm_indices[((self.flag==3) | (self.flag==4)),:] = torch.tensor(self.left_arm_indices,dtype=torch.int,device=self.device)
        # arm_indices = self.left_arm_indices[:5] + [self.left_arm_indices[6]] + self.right_arm_indices[:5] + [self.right_arm_indices[6]]
        env_indices =  torch.arange(self.num_envs,device=self.device).unsqueeze(1).expand_as(arm_indices)
        self.robot_dof_targets[env_indices, arm_indices] = self.robot_dof_targets[env_indices,arm_indices] + self.actions[:,:7] * self.action_scale   # 给左臂和右臂赋值
        
        targets = self.robot_dof_targets
        self.robot_dof_targets[:] = torch.clamp(targets, self.robot_dof_lower_limits, self.robot_dof_upper_limits)
        self.robot_dof_targets[:,self.right_arm_indices[5:]] = self.limit_six_seven(self.robot_dof_targets[:,self.right_arm_indices[5:]])
        self.robot_dof_targets[:,self.left_arm_indices[5:]] = self.limit_six_seven(self.robot_dof_targets[:,self.left_arm_indices[5:]])

    # 执行动作
    def _apply_action(self):
        # 驱动关节
        self._robot.set_joint_position_target(self.robot_dof_targets)

    # post-physics step calls
    # 判断回合有没有结束
    def _get_dones(self) -> tuple[torch.Tensor, torch.Tensor]:
        print('self.flag',self.flag[:20])
        # 定义主要变量
        self.d_cur = torch.zeros((self.num_envs, 1), dtype=torch.float32, device=self.device)
        self.z_dif_cur = torch.zeros((self.num_envs, 1), dtype=torch.float32, device=self.device)
        self.rot_cur = torch.zeros((self.num_envs,1), dtype=torch.float32, device=self.device)

        # print(self._robot.data.joint_pos[:,self.left_arm_indices]-self.robot_dof_targets[:,self.left_arm_indices])
#=========================================================计算第一阶段的终止条件==========================================================================
        # 右手主动抓取饮料瓶
        kettle_pos2 = self._kettle.data.root_com_pos_w.clone()      # 水壶中心位置
        kettle_pos2 = kettle_pos2.unsqueeze(1).repeat(1,len(self.right_hand_proximal_indices),1)
        kettle_pos2[:,0,2]+= 0.02   # 调整手指的目标距离
        kettle_pos2[:,1,2]+= 0.01
        kettle_pos2[:,3,2]-= 0.03
        kettle_pos2[:,4,2]-= 0.05
        right_hand_proximal_state  = self._robot.data.body_link_state_w[:,self.right_hand_proximal_indices,0:7].clone()    # 左臂第7关节值
        d_cur = torch.mean(torch.norm(right_hand_proximal_state[:,:,:2] - kettle_pos2[:,:,:2], p=2, dim=-1),dim=-1)
        self.d_cur[(self.flag == 0) | (self.flag == 1)]  = d_cur.unsqueeze(1)[(self.flag == 0) | (self.flag == 1)] 
        z_dif_cur = torch.mean(torch.abs(right_hand_proximal_state[:,:,2] - kettle_pos2[:,:,2]), -1) # 计算z轴的差值
        self.z_dif_cur[(self.flag == 0) | (self.flag == 1)]  = z_dif_cur.unsqueeze(1)[(self.flag == 0) | (self.flag == 1)] 
        # self.k_r_d_cur = torch.cat((self.k_r_d_cur.unsqueeze(-1), z_dif_r.unsqueeze(-1)), dim=-1)
        right_link7_state  = self._robot.data.body_link_state_w[:,self.right_link7_idx,0:7].clone()   # 右臂第7关节值
        rot_cur = torch.pi - self.calculate_deflection_angle(right_link7_state[:,3:])
        self.rot_cur[(self.flag == 0) | (self.flag == 1)]  = rot_cur[(self.flag == 0) | (self.flag == 1)] 
        # self.flag[torch.all(self.k_r_d_cur <= 0.08, dim=-1) & torch.all(self.c_l_d_cur <= 0.1, dim=-1)  & ((self.c_rot_cur < 0.2).squeeze(1)) & ((self.k_rot_cur < 0.2).squeeze(1))] = 1
        # self.flag[~(torch.all(self.k_r_d_cur <= 0.08, dim=-1) & torch.all(self.c_l_d_cur <= 0.1, dim=-1)  & ((self.c_rot_cur < 0.2).squeeze(1)) & ((self.k_rot_cur < 0.2).squeeze(1)))] = 0
        target_1 = (self.d_cur <= 0.07).squeeze(1)
        target_2 = (self.rot_cur < 0.1).squeeze(1)
        target_3 = (self.z_dif_cur < 0.02).squeeze(1)

        self.flag[target_1 & target_2 & target_3 & (self.flag==0)] = 1
        self.flag[(~(target_1 & target_2 & target_3)) & (self.flag==1)] = 0

        # self.flag_1to2 = torch.tensor([False]*self.num_envs,device=self.device)  # 是否抓取成功
        # # 抓取成功否判断
        # threthod_force = 2
        # term_right_hand_grasp = torch.tensor([False]*self.num_envs,device=self.device)
        # if self._contact_forces_right_hand_intermediate.data.force_matrix_w != None:
        #     term_right_hand_grasp1 = torch.any(torch.abs(self._contact_forces_right_hand_intermediate.data.force_matrix_w) > threthod_force, dim=-1).squeeze(1)
        #     # print('term_right_hand_grasp1.shape',term_right_hand_grasp1.shape)
        #     term_right_hand_grasp = torch.sum(term_right_hand_grasp1,dim=1) >= 3
        #     # print('contact_Force',self._contact_forces_right_hand_intermediate.data.force_matrix_w[:2])
        # self.flag_1to2[term_right_hand_grasp & (self.flag == 1) & (torch.all(self.d_cur<0.08,dim=1))] = True
        # self.flag[term_right_hand_grasp & (self.flag == 1) & (torch.all(self.d_cur<0.08,dim=1))] = 2

#=========================================================计算第二阶段的终止条件==========================================================================
        # 水杯的位置判断
        cup_pos2 = self._cup.data.root_com_pos_w.clone()      # 水壶中心位置
        cup_pos2 = cup_pos2.unsqueeze(1).repeat(1,len(self.left_hand_proximal_indices),1)
        cup_pos2[:,0,2]+= 0.01   # 调整手指的目标距离
        cup_pos2[:,1,2]+= 0.03
        cup_pos2[:,2,2]+= 0.01
        cup_pos2[:,3,2]-= 0.00
        cup_pos2[:,4,2]-= 0.02
        left_hand_proximal_state  = self._robot.data.body_link_state_w[:,self.left_hand_proximal_indices,0:7].clone()    # 左臂第7关节值
        d_cur = torch.mean(torch.norm(left_hand_proximal_state[:,:,:2] - cup_pos2[:,:,:2], p=2, dim=-1),dim=-1)
        self.d_cur[(self.flag == 3) | (self.flag == 4)]  = d_cur.unsqueeze(-1)[(self.flag == 3) | (self.flag == 4)] 
        z_dif_cur = torch.mean(torch.abs(left_hand_proximal_state[:,:,2] - cup_pos2[:,:,2]), dim=-1 )# 计算z轴的差值
        self.z_dif_cur[(self.flag == 3) | (self.flag == 4)]  = z_dif_cur.unsqueeze(-1)[(self.flag == 3) | (self.flag == 4)] 
        # self.c_l_d_cur = torch.cat((self.c_l_d_cur.unsqueeze(-1), z_dif_l.unsqueeze(-1)), dim=-1)
        left_link7_state  = self._robot.data.body_link_state_w[:,self.left_link7_idx,0:7].clone()   # 左臂第7关节值
        rot_cur = self.calculate_deflection_angle(left_link7_state[:,3:]) 
        self.rot_cur[(self.flag == 3) | (self.flag == 4)]  = rot_cur[(self.flag == 3) | (self.flag == 4)] 
        # self.flag[torch.all(self.k_r_d_cur <= 0.08, dim=-1) & torch.all(self.c_l_d_cur <= 0.1, dim=-1)  & ((self.c_rot_cur < 0.2).squeeze(1)) & ((self.k_rot_cur < 0.2).squeeze(1))] = 1
        # self.flag[~(torch.all(self.k_r_d_cur <= 0.08, dim=-1) & torch.all(self.c_l_d_cur <= 0.1, dim=-1)  & ((self.c_rot_cur < 0.2).squeeze(1)) & ((self.k_rot_cur < 0.2).squeeze(1)))] = 0
        target_4 = (self.d_cur <= 0.08).squeeze(1)
        target_5 = (self.rot_cur < 0.1).squeeze(1)
        target_6 = (self.z_dif_cur < 0.011).squeeze(1)

        self.flag[(target_4 & target_5 & target_6) & (self.flag==3)] = 4
        self.flag[(~(target_4 & target_5 & target_6)) & (self.flag==4)] = 3

        # self.flag_4to5 = torch.tensor([False]*self.num_envs,device=self.device)  # 左手是否抓取成功
        # # 抓取成功否判断
        # threthod_force = 2
        # term_left_hand_grasp = torch.tensor([False]*self.num_envs,device=self.device)
        # if self._contact_forces_left_hand_intermediate.data.force_matrix_w != None:
        #     term_left_hand_grasp1 = torch.any(torch.abs(self._contact_forces_left_hand_intermediate.data.force_matrix_w) > threthod_force, dim=-1).squeeze(1)
        #     # print('term_right_hand_grasp1.shape',term_right_hand_grasp1.shape)
        #     term_left_hand_grasp = torch.sum(term_left_hand_grasp1,dim=1) >= 3
        #     # print('contact_Force',self._contact_forces_left_hand_intermediate.data.force_matrix_w[:2])
        # self.flag_4to5[term_left_hand_grasp & (self.flag == 4) & (torch.all(self.d_cur<0.1,dim=1))] = True
        # self.flag[term_left_hand_grasp & (self.flag == 4) & (torch.all(self.d_cur<0.1,dim=1))] = 5

        #==========判断flag====================
        # 杯子、水壶以及桌子是否挪动
        self.detect_kettle_cup_move_t = self.detect_kettle_cup_move_t + 1
        kettle_root_pos = self._kettle.data.root_pos_w
        self.kettle_change_pos[self.detect_kettle_cup_move_t == 5] = kettle_root_pos[self.detect_kettle_cup_move_t == 5]
        self.k_change1 = torch.any(torch.abs(kettle_root_pos[:,:2] - self.kettle_change_pos[:,:2]) > 0.002, dim=1) & (self.flag == 0) & (self.detect_kettle_cup_move_t > 5)
        self.k_change2 = torch.any(torch.abs(kettle_root_pos[:,:2] - self.kettle_change_pos[:,:2]) > 0.002, dim=1) & (self.flag == 1) & (self.detect_kettle_cup_move_t > 5)
        self.k_change = self.k_change1 | self.k_change2
        k_change = self.k_change.unsqueeze(1)

        cup_root_pos = self._cup.data.root_pos_w
        self.cup_change_pos[self.detect_kettle_cup_move_t == 5] = cup_root_pos[self.detect_kettle_cup_move_t == 5]
        self.c_change1 = torch.any(torch.abs(cup_root_pos[:,:2] - self.cup_change_pos[:,:2]) > 0.002, dim=1) & ((self.flag == 3)) & (self.detect_kettle_cup_move_t > 5)
        self.c_change2 = torch.any(torch.abs(cup_root_pos[:,:2] - self.cup_change_pos[:,:2]) > 0.002, dim=1) & ((self.flag == 4)) & (self.detect_kettle_cup_move_t > 5)
        self.c_change = self.c_change1 | self.c_change2
        c_change = self.c_change.unsqueeze(1)

        self.term_arm_hand = torch.any(torch.abs(self._contact_sensor_table.data.force_matrix_w) > 0.01,dim=(1,2,3))
        term_arm_hand = self.term_arm_hand.unsqueeze(1)
        # if torch.any(term_arm_hand):
        #     print('term_arm_hand  ',term_arm_hand)
        terminated_0 = torch.any(torch.concat((c_change,k_change,term_arm_hand), dim=1),dim=1)  # 
        
        
        # terminated = terminated_0 | ((self._kettle.data.root_com_pos_w[:,2] < (self.kettle_z-0.01)) & (self.detect_kettle_cup_move_t > 5)) #terminated_2
        # print("terminated",terminated)
        # self.Hand_end = (self.grasp_intent >= 0.8)
        # terminated = terminated | self.Hand_end
        terminated = terminated_0
        # terminated = terminated | (self.flag == 2) | (self.flag == 5)
        truncated = self.episode_length_buf >= self.max_episode_length - 1

        self.end = terminated | truncated

        # print("truncated",truncated)
        # if torch.any(terminated):
        #     print('terminated',terminated)
        #     print('k_change',k_change)
        #     print('term_arm_hand',term_arm_hand)
        return terminated, truncated

    def _get_rewards(self) -> torch.Tensor:
        # 将指标归一化到[0, 1]之间
        self.max_d = 2.0
        self.max_z = 1.0
        self.max_rot = torch.pi

        norm_d_cur = torch.clamp(self.d_cur / self.max_d, 0, 1)
        norm_z_diff_cur = torch.clamp(self.z_dif_cur / self.max_z, 0, 1)
        norm_rot_cur = torch.clamp(self.rot_cur / self.max_rot, 0, 1)

        norm_d_last = torch.clamp(self.region_d / self.max_d, 0, 1)
        norm_z_dif_last = torch.clamp(self.region_z_dif / self.max_z, 0, 1)
        norm_rot_last = torch.clamp(self.region_rot / self.max_rot, 0, 1)


        print("d ",self.d_cur[:4])
        print("z_diff ",self.z_dif_cur[:4])
        print("rot ",self.rot_cur[:4])

        # ------------------------------ 支配关系 --------------------------------------------------
        # 定义一个函数判断当前指标是否支配了前一个指标
        def dominates(new_region, prev_region):
            return torch.all(new_region <= prev_region, dim = 1)
            # return torch.all((new_region - prev_region)<0.01, dim = 1) & torch.any(new_region < prev_region, dim = 1)
        # 定义一个函数判断当前指标是否被支配了前一个指标
        def dominated(new_region, prev_region):
            return torch.all(new_region > prev_region, dim = 1)
            # return torch.all((new_region - prev_region)<0.01, dim = 1) & torch.any(new_region < prev_region, dim = 1)

        # ------------------------------ 联合奖励 --------------------------------------------------
        # 初始化奖励
        rewards = torch.zeros(self.num_envs, dtype=torch.float, device=self.device)

        # 只有新的指标支配了上一个指标时，才给奖励
        cur_index = torch.cat((norm_d_cur,norm_z_diff_cur,norm_rot_cur),dim=1)
        last_index = torch.cat((norm_d_last,norm_z_dif_last, norm_rot_last),dim=1)
        condition_d = dominates(cur_index, last_index) # & (self.actions[:,-1]>-1)
        condition_d_V_R = torch.all((last_index - cur_index)>((last_index-torch.tensor([0.03,0.01,0.03],device=self.device))*0.03),dim=-1)
        condition_d_V_L = (torch.norm(last_index,p=2,dim=-1) - torch.norm(cur_index,p=2,dim=-1)) > 0.004
        condition_ed = dominated(cur_index, last_index)
        condition_not_d = ~(condition_d | condition_ed)
        condition_t = (torch.norm(cur_index,dim=1,p=2) <= 0.05) # | (self.flag == 2)

        print('condition_d_V ',condition_d_V_R[condition_d])
        rewards[condition_d & condition_d_V_R & ((self.flag == 0)| (self.flag == 1))] = 1
        rewards[condition_d & condition_d_V_L & ((self.flag == 3)| (self.flag == 4))] = 1.1
        rewards[condition_ed ] = -1.0
        rewards[condition_not_d]  = -0.5

        # print('     ')
        print('     ')

        # ------------------------------ 最终奖励 --------------------------------------------------
        print('self.threhold_curi',self.threhold_curi)
        # rewards[self.Hand_end] += -5.0

        # rewards[(self.flag == 0) & (self.d_cur.squeeze() < 0.2) & (self.z_dif_cur.squeeze() < 0.05) & (self.rot_cur.squeeze() < 0.3)] += 10.0
        
        #  达到终点，即给奖励，优先级第二
        rewards[self.flag == 1] = 30.0
        # rewards[self.flag_1to2] = 30.0
        rewards[self.flag == 4] = 30.0
        # rewards[self.flag_4to5] = 30.0

        # 惩罚项放在最后，一旦触发必执行，优先级最高
        rewards[self.k_change | self.c_change] = -10.0
        rewards[self.term_arm_hand] = -20.0

        print('rewards',rewards[:20])

        return rewards

    # 复原函数
    def _reset_idx(self, env_ids: torch.Tensor | None):
        if env_ids is None:
            env_ids = self._robot._ALL_INDICES
        super()._reset_idx(env_ids)
        # 机器人状态重置
        robot_state = self._robot.data.default_root_state.clone()
        robot_id_state = robot_state[env_ids, :].clone()
        robot_id_state[:, 2]  = robot_id_state[:, 2] + sample_uniform(
            -0.03,
            0.03,
            len(env_ids),
            self.device,
        )
        robot_id_state[:, :3] += self.scene.env_origins[env_ids, :]
        self._robot.write_root_link_pose_to_sim(robot_id_state[:, :7],env_ids=env_ids)
        self._robot.write_root_com_velocity_to_sim(robot_id_state[:, 7:],env_ids=env_ids)
        joint_pos = self._robot.data.default_joint_pos[env_ids]
        joint_pos = torch.clamp(joint_pos, self.robot_dof_lower_limits, self.robot_dof_upper_limits)
        joint_vel = torch.zeros_like(joint_pos)
        self._robot.set_joint_position_target(joint_pos, env_ids=env_ids)
        self._robot.write_joint_state_to_sim(joint_pos, joint_vel, env_ids=env_ids)

        # 杯子和饮料瓶状态重置
        kettle_orinal_pos = self.kettle_orinal_pos[env_ids, :].clone() 
        cup_orinal_pos = self.cup_orinal_pos[env_ids, :].clone()
        kettle_orinal_pos[:, 0]  = kettle_orinal_pos[:, 0] + sample_uniform(
            -self.threhold_curi,
            self.threhold_curi,
            len(env_ids),
            self.device,
        )
        kettle_orinal_pos[:, 1] = kettle_orinal_pos[:, 1] + sample_uniform(
            -self.threhold_curi,
            self.threhold_curi,
            len(env_ids),
            self.device,
        )

        cup_orinal_pos[:, 0] = cup_orinal_pos[:, 0] + sample_uniform(
            -self.threhold_curi,
            self.threhold_curi,
            len(env_ids),
            self.device,
        )
        cup_orinal_pos[:, 1] = cup_orinal_pos[:, 1] + sample_uniform(
            -self.threhold_curi,
            self.threhold_curi,
            len(env_ids),
            self.device,
        )
        
        self._kettle.write_root_pose_to_sim(torch.cat((kettle_orinal_pos,self.kettle_orinal_quat[env_ids,:]),dim=-1), env_ids)
        self._cup.write_root_pose_to_sim(torch.cat((cup_orinal_pos,self.cup_orinal_quat[env_ids,:]),dim=-1), env_ids)
        self._table.write_root_pose_to_sim(torch.cat((self.table_orinal_pos[env_ids,:],self.table_orinal_quat[env_ids,:]),dim=-1), env_ids)
        self._kettle.write_root_velocity_to_sim(torch.zeros((len(env_ids), 6), device=self.device), env_ids)
        self._cup.write_root_velocity_to_sim(torch.zeros((len(env_ids), 6), device=self.device), env_ids)
        self._table.write_root_velocity_to_sim(torch.zeros((len(env_ids), 6), device=self.device), env_ids)
        # 关节位置和速度变量重置
        pre_left_joint_p= self._robot.data.joint_pos[:,self.left_arm_indices].clone()   # 上一个关节位置
        pre_right_joint_p = self._robot.data.joint_pos[:,self.right_arm_indices].clone()   # 上一个关节位置
        self.pre_left_joint_p[env_ids] = pre_left_joint_p[env_ids,:].clone()   # 上一个关节位置
        self.pre_right_joint_p[env_ids] = pre_right_joint_p[env_ids,:].clone()   # 上一个关节位置置
        self.pre_left_joint_v[env_ids]  = torch.zeros((len(env_ids), 7), device=self.device)  # 上一个关节速度
        self.pre_right_joint_v[env_ids]  = torch.zeros((len(env_ids), 7), device=self.device) # 上一个关节速度

        self.detect_kettle_cup_move_t[env_ids] = 0
        self.image_data_revise.update({k.item():5 for k in env_ids})
        self.grasp_intent[env_ids] = 0
        rand_flag_value = torch.randint(0,2,(len(env_ids),),device=self.device)
        rand_flag_value[rand_flag_value==1] = 3
        self.flag[env_ids] = rand_flag_value

    # 获取观测值函数
    def _get_observations(self) -> dict:
        #=====================================定义主要函数==========================================
        # def fps(points, npoint):
        #     """
        #     对一个点集执行 FPS 采样
        #     :param points: Tensor, (N, 3)
        #     :param npoint: int, 采样个数
        #     :return: Tensor, (npoint, 3)
        #     """
        #     N, _ = points.shape
        #     device = points.device

        #     centroids = torch.zeros(npoint, dtype=torch.long, device=device)
        #     distance = torch.ones(N, device=device) * 1e10
        #     farthest = torch.randint(0, N, (1,), dtype=torch.long, device=device)

        #     for i in range(npoint):
        #         centroids[i] = farthest
        #         centroid = points[farthest, :].view(1, 3)
        #         dist = torch.sum((points - centroid) ** 2, dim=1)
        #         mask = dist < distance
        #         distance[mask] = dist[mask]
        #         farthest = torch.max(distance, dim=0)[1]

        #     return points[centroids]


        # def batch_fps_tensor(point_dict, npoint):
        #     """
        #     对字典中每个点集执行 FPS 采样，并堆叠成 Tensor (B, npoint, 3)
        #     :param point_dict: dict, key: int -> value: Tensor(N_i, 3)
        #     :param npoint: int, 每个点集采样个数
        #     :return: Tensor, (B, npoint, 3)
        #     """
        #     sampled_list = []
        #     sorted_keys = sorted(point_dict.keys())  # 保证顺序一致
        #     for key in sorted_keys:
        #         points = point_dict[key]
        #         if points.shape[0] < npoint:
        #             raise ValueError(f"点集 {key} 的点数 {points.shape[0]} 少于采样点数 {npoint}")
        #         # 输入点云：[batch_size, num_points, 3]，GPU 张量

        #         # FPS 采样（返回索引和坐标）
        #         sampled_points, indices = sample_farthest_points(points.unsqueeze(0), K=npoint, random_start_point=True)  # indices.shape: [2, K]
        #         # print(sampled_points.shape)
                
        #         # sampled = fps(points, npoint)
        #         sampled_list.append(sampled_points.squeeze())
        #     return torch.stack(sampled_list, dim=0)

        def quaternion_inverse(q):
            """计算四元数的逆（假设四元数是单位四元数）"""
            q_conj = np.array([q[0], -q[1], -q[2], -q[3]])  # 共轭
            return q_conj / np.dot(q, q)  # 归一化（如果不是单位四元数）

        def quaternion_multiply(q1, q2):
            """计算两个四元数的乘积"""
            w1, x1, y1, z1 = q1
            w2, x2, y2, z2 = q2
            return np.array([
                w1*w2 - x1*x2 - y1*y2 - z1*z2,
                w1*x2 + x1*w2 + y1*z2 - z1*y2,
                w1*y2 - x1*z2 + y1*w2 + z1*x2,
                w1*z2 + x1*y2 - y1*x2 + z1*w2
            ])

        def rotate_vector_by_quaternion(v, q):
            """使用四元数旋转三维向量"""
            q_vec = np.hstack(([0], v))  # 扩展为四元数形式
            q_rotated = quaternion_multiply(quaternion_multiply(q, q_vec), quaternion_inverse(q))
            return q_rotated[1:]  # 提取旋转后的向量

        def transform_pose(q_A_B, p_A_B, q_C_B, p_C_B):
            """已知A在B的位姿，C在B的位姿，计算A在C坐标系下的四元数和笛卡尔坐标"""
            q_C_B_inv = quaternion_inverse(q_C_B)
            q_A_C = quaternion_multiply(q_C_B_inv, q_A_B)
            
            p_rel = p_A_B - p_C_B  # A相对C的平移
            p_A_C = rotate_vector_by_quaternion(p_rel, q_C_B_inv)  # 变换到C坐标系
            
            return q_A_C, p_A_C
        
        def transform_p(p_A_B, q_C_B, p_C_B):
            """已知A在B的位置，C在B的位姿，计算A在C坐标系下的笛卡尔坐标"""
            q_C_B_inv = quaternion_inverse(q_C_B)
            
            p_rel = p_A_B - p_C_B  # A相对C的平移
            p_A_C = rotate_vector_by_quaternion(p_rel, q_C_B_inv)  # 变换到C坐标系
            
            return p_A_C
        
        def transform_q(q_A_B, q_C_B):
            """已知A在B的位置，C在B的位姿，计算A在C坐标系下的笛卡尔坐标"""
            q_C_B_inv = quaternion_inverse(q_C_B)
            q_A_C = quaternion_multiply(q_C_B_inv, q_A_B)
            
            return q_A_C

        def quaternion_to_rotation_matrix_batch(q: torch.Tensor) -> torch.Tensor:
            """
            将批量四元数转换为批量旋转矩阵
            输入: q (N, 4)，格式为 (x, y, z, w)
            输出: R (N, 3, 3)
            """
            w, x, y, z = q[:, 0], q[:, 1], q[:, 2], q[:, 3]
            xx, yy, zz = x*x, y*y, z*z
            xy, xz, yz = x*y, x*z, y*z
            xw, yw, zw = x*w, y*w, z*w

            R = torch.stack([
                1 - 2*(yy + zz),     2*(xy - zw),       2*(xz + yw),
                2*(xy + zw),         1 - 2*(xx + zz),   2*(yz - xw),
                2*(xz - yw),         2*(yz + xw),       1 - 2*(xx + yy)
            ], dim=1).reshape(-1, 3, 3)  # (N, 3, 3)

            return R

        def transform_points_batch_full(points_a: torch.Tensor, q_AB: torch.Tensor, t_AB: torch.Tensor) -> torch.Tensor:
            """
            将一批 (N) 个坐标系下的 M 个点从 A 转换到 B

            Args:
                points_a: (N, M, 3) A 坐标系下的点
                q_AB: (N, 4) A 相对于 B 的四元数 (x, y, z, w)
                t_AB: (N, 3) A 相对于 B 的平移向量

            Returns:
                points_b: (N, M, 3) B 坐标系下的点
            """
            R_AB = quaternion_to_rotation_matrix_batch(q_AB)  # (N, 3, 3)

            # batch 矩阵乘法：对每个 R_AB[i] 和 points_a[i] 的每个点做乘法
            points_rotated = torch.matmul(points_a, R_AB.transpose(1, 2))  # (N, M, 3)

            # 加上平移向量，自动 broadcast 到每个点
            points_b = points_rotated + t_AB[:, None, :]  # (N, M, 3)

            return points_b

        def replace_large_x_with_masked_mean(points):
            # points: (B, N, 3)
            B, N, _ = points.shape

            # Step 1: 创建 mask：x > 0.7 的点为 True
            mask = (points[..., 0] > 0.7) | (points[..., 0] < 0.3)  # shape: (B, N)

            # Step 2: 创建反向 mask：x <= 0.7 的点为 True，用于计算平均值
            valid_mask = ~mask  # (B, N)

            # Step 3: 为了广播计算平均值，扩展 valid_mask 维度
            valid_mask_exp = valid_mask.unsqueeze(-1)  # (B, N, 1)

            # 避免无效点干扰，将无效位置置为 0
            masked_points = points * valid_mask_exp  # (B, N, 3)

            # 每个 batch 中有效点数量，用于求平均
            valid_counts = valid_mask.sum(dim=1, keepdim=True).clamp(min=1)  # 防止除0，(B, 1)

            # 求每个 batch 的有效均值（只考虑 x <= 0.7 的点）
            masked_mean = masked_points.sum(dim=1, keepdim=True) / valid_counts.unsqueeze(-1)  # (B, 1, 3)

            # Step 4: 用 masked_mean 替换 x > 0.7 的点
            mask_exp = mask.unsqueeze(-1).expand(-1, -1, 3)  # (B, N, 3)
            result = torch.where(mask_exp, masked_mean.expand(-1, N, -1), points)

            return result

        def apply_polygon_mask(depth_map, num_vertices=6, irregularity=0.5, spikiness=0.4, scale=30):
            """
            给一个 (N, 180, 320) 的深度图张量添加不规则多边形遮挡区域（深度置0）。
            """
            N, H, W = depth_map.shape
            device = depth_map.device
            depth_map_np = depth_map.cpu().numpy()

            for i in range(N):
                # 生成中心点
                center_x = np.random.randint(W // 4, 3 * W // 4)
                center_y = np.random.randint(H // 4, 3 * H // 4)

                # 生成随机多边形顶点
                angle_steps = np.linspace(0, 2 * np.pi, num_vertices, endpoint=False)
                angles = angle_steps + np.random.uniform(-irregularity, irregularity, size=angle_steps.shape)
                lengths = np.random.uniform((1 - spikiness) * scale, (1 + spikiness) * scale, size=angles.shape)

                pts = []
                for angle, r in zip(angles, lengths):
                    x = int(center_x + r * np.cos(angle))
                    y = int(center_y + r * np.sin(angle))
                    # 保证点在图像内
                    x = np.clip(x, 0, W - 1)
                    y = np.clip(y, 0, H - 1)
                    pts.append([x, y])

                pts = np.array([pts], dtype=np.int32)

                # 创建遮挡mask
                mask = np.ones((H, W), dtype=np.float32)
                cv2.fillPoly(mask, pts, 0.0)

                # 应用到 depth map 上
                depth_map_np[i] *= mask

            return torch.tensor(depth_map_np, device=device)


        start_time = time.time()
        # 定义主要变量
        self.region_d = torch.zeros((self.num_envs, 1), dtype=torch.float32, device=self.device)
        self.region_z_dif = torch.zeros((self.num_envs, 1), dtype=torch.float32, device=self.device)
        self.region_rot = torch.zeros((self.num_envs,1), dtype=torch.float32, device=self.device)
        
        # 水壶的位置距离判断
        kettle_pos2 = self._kettle.data.root_com_pos_w.clone()      # 水壶中心位置
        kettle_pos2 = kettle_pos2.unsqueeze(1).repeat(1,len(self.right_hand_proximal_indices),1)
        kettle_pos2[:,0,2]+= 0.02   # 调整手指的目标距离
        kettle_pos2[:,1,2]+= 0.01
        kettle_pos2[:,3,2]-= 0.03
        kettle_pos2[:,4,2]-= 0.05
        # print('kettle_pos2  ',kettle_pos2)
        right_hand_proximal_state  = self._robot.data.body_link_state_w[:,self.right_hand_proximal_indices,0:7].clone()    # 
        right_link7_state  = self._robot.data.body_link_state_w[:,self.right_link7_idx,0:7].clone()   # 右臂第7关节值
        # print('right_hand_proximal_state[:,:,:3] ',right_hand_proximal_state[:,:,:3])
        region_d = torch.mean(torch.norm(right_hand_proximal_state[:,:,:2] - kettle_pos2[:,:,:2], p=2, dim=-1),dim=-1)
        self.region_d[(self.flag == 0)|(self.flag == 1)] = region_d.unsqueeze(-1)[(self.flag == 0)|(self.flag == 1)] 
        region_z_dif = torch.mean(torch.abs(right_hand_proximal_state[:,:,2] - kettle_pos2[:,:,2]), -1) # 计算z轴的差值
        self.region_z_dif[(self.flag == 0)|(self.flag == 1)]  = region_z_dif.unsqueeze(-1)[(self.flag == 0)|(self.flag == 1)] 
        # self.region_k_r_d = torch.cat((self.region_k_r_d.unsqueeze(-1), z_dif_r.unsqueeze(-1)), dim=-1)
        region_rot = torch.pi - self.calculate_deflection_angle(right_link7_state[:,3:])
        self.region_rot[(self.flag == 0)|(self.flag == 1)] = region_rot[(self.flag == 0)|(self.flag == 1)]

        # 水杯的位置判断
        cup_pos2 = self._cup.data.root_com_pos_w.clone()      # 水壶中心位置
        cup_pos2 = cup_pos2.unsqueeze(1).repeat(1,len(self.left_hand_proximal_indices),1)
        cup_pos2[:,0,2]+= 0.01   # 调整手指的目标距离
        cup_pos2[:,1,2]+= 0.03
        cup_pos2[:,2,2]+= 0.01
        cup_pos2[:,3,2]-= 0.00
        cup_pos2[:,4,2]-= 0.02
        left_hand_proximal_state  = self._robot.data.body_link_state_w[:,self.left_hand_proximal_indices,0:7].clone()    # 左臂第7关节值
        region_d = torch.mean(torch.norm(left_hand_proximal_state[:,:,:2] - cup_pos2[:,:,:2], p=2, dim=-1),dim=-1)
        self.region_d[(self.flag == 3)|(self.flag == 4)] = region_d.unsqueeze(-1)[(self.flag == 3)|(self.flag == 4)]
        region_z_dif = torch.mean(torch.abs(left_hand_proximal_state[:,:,2] - cup_pos2[:,:,2]), dim=-1 )# 计算z轴的差值
        self.region_z_dif[(self.flag == 3)|(self.flag == 4)] = region_z_dif.unsqueeze(-1)[(self.flag == 3)|(self.flag == 4)]
        # self.region_c_l_d = torch.cat((self.region_c_l_d.unsqueeze(-1), z_dif_l.unsqueeze(-1)), dim=-1)
        left_link7_state  = self._robot.data.body_link_state_w[:,self.left_link7_idx,0:7].clone()   # 左臂第7关节值
        region_rot = self.calculate_deflection_angle(left_link7_state[:,3:]) 
        self.region_rot[(self.flag == 3)|(self.flag == 4)] = region_rot[(self.flag == 3)|(self.flag == 4)]


        keys_to_remove = [k for k, v in self.image_data_revise.items() if v == 0]
        # 删除这些键
        for k in keys_to_remove:
            del self.image_data_revise[k]
        env_list = list(self.image_data_revise.keys())

        # env_list = [i for i in range(self.num_envs)]

        # new_env_list = list(self.image_data_revise.keys())
        # flag_env_list = self.flag_1to2.nonzero().squeeze().tolist()
        # env_list = new_env_list + flag_env_list

        print('env_list', env_list)
        # 通过深度图像获取目标区域点云
        if len(env_list)>0:
            camera_data_depth = self._camera.data.output["depth"]
            camera_data_depth = camera_data_depth.squeeze(-1)
            print('camera_data_depth.shape', camera_data_depth.shape)
            camera_data_depth = camera_data_depth[env_list,:,:]
            camera_data_depth = apply_polygon_mask(camera_data_depth)
            Height,Width = camera_data_depth.shape[1], camera_data_depth.shape[2]
            
            u, v = torch.meshgrid(torch.arange(Width,device=self.device), torch.arange(Height,device=self.device),indexing='xy')
            u = u.unsqueeze(0).repeat(len(env_list),1,1)
            v = v.unsqueeze(0).repeat(len(env_list),1,1)
            # 反投影为相机坐标
            x = (u - self.cx) * camera_data_depth / self.fx
            y = (v - self.cy) * camera_data_depth / self.fy
            # print(x.shape, y.shape, camera_data_depth.shape)
            # 拼成点云 (N, 3)
            points_cam = torch.zeros(len(env_list), Height, Width, 3, device=self.device)
            points_cam[:,:,:,0] = x
            points_cam[:,:,:,1] = y
            points_cam[:,:,:,2] = camera_data_depth

            left_point_cam = points_cam[:,:,:Width//2,:]
            right_point_cam = points_cam[:,:,Width//2:,:]
            left_point_cam = left_point_cam.reshape(len(env_list), -1, 3)
            right_point_cam = right_point_cam.reshape(len(env_list), -1, 3)

            # 获取相机在世界坐标系下的位姿
            camera_pos = self._camera.data.pos_w
            camera_quat = self._camera.data.quat_w_ros

            Q_camera_robot = torch.zeros((len(env_list), 4), device=self.device)
            P_camera_robot = torch.zeros((len(env_list), 3), device=self.device)
            for i in range(len(env_list)):
                q_robot_W = self._robot.data.root_quat_w[env_list[i],:].clone().cpu().numpy()  # 机器人相对世界的姿态
                p_robot_W = self._robot.data.root_pos_w[env_list[i],:].clone().cpu().numpy()  # 机器人在世界下的坐标
                q_camera_robot, p_camera_robot = transform_pose(camera_quat[env_list[i]].cpu().numpy(), camera_pos[env_list[i]].cpu().numpy(), q_robot_W, p_robot_W)
                # 对点云进行坐标系转换，转换到机器人坐标系下
                Q_camera_robot[i] = torch.tensor(q_camera_robot,device=self.device)
                P_camera_robot[i] = torch.tensor(p_camera_robot,device=self.device)


            left_point_cam = transform_points_batch_full(left_point_cam, Q_camera_robot, P_camera_robot)
            left_point_cam = replace_large_x_with_masked_mean(left_point_cam)

            right_point_cam = transform_points_batch_full(right_point_cam, Q_camera_robot, P_camera_robot)
            right_point_cam = replace_large_x_with_masked_mean(right_point_cam)
            

            points_aoi_left, _  = sample_farthest_points(left_point_cam, K=800, random_start_point=True)
            # points_aoi_left[:,:,2] += 0.06
            points_aoi_right, _  = sample_farthest_points(right_point_cam, K=800, random_start_point=True)



            
            # del points_cam

            # print('self.image_data_revise',self.image_data_revise)
            self.target_decect_data[env_list,:] = torch.cat((points_aoi_left.reshape(-1,800*3),points_aoi_right.reshape(-1,800*3)),dim=-1)

        for key in self.image_data_revise:
            self.image_data_revise[key] -= 1

        # 对点云进行fps过滤，减少点云数量

        # points_aoi, _  = sample_farthest_points(points_aoi, K=1000, random_start_point=True)  # indices.shape: [2, K]
        # print((points_aoi==0).nonzero().shape)
        # print(points_aoi.shape)
        # import pyvista as pv
        # points = points_aoi[0].cpu().numpy()
        # # 创建 PyVista 点云对象
        # cloud = pv.PolyData(points)
        # # 可视化
        # plotter = pv.Plotter()
        # plotter.add_points(cloud, color='blue', point_size=5)
        # plotter.show()       

        
        End_pos_quat = torch.zeros((self.num_envs, 14), device=self.device)
        # 示例四元数和位移向量（w, x, y, z 格式）

        for i in range(self.num_envs):
            q_leftArm_W = left_link7_state[i,3:].cpu().numpy() # 左臂相对世界的姿态
            p_leftArm_W = left_link7_state[i,:3].cpu().numpy()  # 左臂在世界下的坐标

            q_rightArm_W = right_link7_state[i,3:].cpu().numpy() # 右臂相对世界的姿态
            p_rightArm_W = right_link7_state[i,:3].cpu().numpy()  # 右臂在世界下的坐标

            q_robot_W = self._robot.data.root_quat_w[i,:].clone().cpu().numpy()  # 机器人相对世界的姿态
            p_robot_W = self._robot.data.root_pos_w[i,:].clone().cpu().numpy()  # 机器人在世界下的坐标
            # print('p_C_B',p_C_B)

            q_leftArm_robot, p_leftArm_robot = transform_pose(q_leftArm_W, p_leftArm_W, q_robot_W, p_robot_W)
            q_rightArm_robot, p_rightArm_robot = transform_pose(q_rightArm_W, p_rightArm_W, q_robot_W, p_robot_W)



            End_pos_quat[i,:7] = torch.tensor(np.hstack((p_leftArm_robot, q_leftArm_robot)),device=self.device)    # 左臂末端相对机器人的位姿
            End_pos_quat[i,7:] = torch.tensor(np.hstack((p_rightArm_robot, q_rightArm_robot)),device=self.device)    # 右臂末端相对机器人的位姿

            # Object_poses[i,:3] = torch.tensor(transform_p(self.target_decect_data[i,:3].cpu().numpy(), q_C_B, p_C_B),device=self.device) 
            # Object_poses[i,3:] = torch.tensor(transform_p(self.target_decect_data[i,3:].cpu().numpy(), q_C_B, p_C_B),device=self.device) 

            # Object_quat[i,:4] = torch.tensor(transform_q([0.5, 0.5, -0.5, -0.5], q_robot_W),device=self.device)
            # Object_quat[i,4:] = torch.tensor(transform_q([0.5, -0.5, 0.5, -0.5], q_robot_W),device=self.device)
        # print('Object_poses',Object_poses[:2])
        # print(transform_quaternion(left_link7_state[0,3:].cpu().numpy(),self._robot.data.root_quat_w[0,:].cpu().numpy()))
    

        # self.cloud.points = self.target_decect_data[0].cpu().numpy()
        # print(self.target_decect_data[0])
        # self.plotter.update()



        # print('time',time.time()-start_time)
        cur_left_joint_p  = self._robot.data.joint_pos[:,self.left_arm_indices].clone()   # 上一个关节位置
        cur_right_joint_p  = self._robot.data.joint_pos[:,self.right_arm_indices].clone()   # 上一个关节位置
        # print('time',time.time()-start_time)
        # print('真实角度差 ',self.robot_dof_targets[:,self.left_arm_indices + self.right_arm_indices] - self._robot.data.joint_pos[:,self.left_arm_indices+ self.right_arm_indices])

        cur_left_joint_v = (cur_left_joint_p - self.pre_left_joint_p) # /self.physics_dt/self.cfg.decimation
        cur_right_joint_v = (cur_right_joint_p - self.pre_right_joint_p)  # /self.physics_dt/self.cfg.decimation
        # print('time',time.time()-start_time)
        self.pre_left_joint_p  = self._robot.data.joint_pos[:,self.left_arm_indices].clone()   # 上一个关节位置
        self.pre_right_joint_p  = self._robot.data.joint_pos[:,self.right_arm_indices].clone()   # 上一个关节位置
        # print('time',time.time()-start_time)
        # self.pre_left_joint_v  = cur_left_joint_v.clone()  # 上一个关节速度
        # self.pre_right_joint_v  = cur_right_joint_v.clone() # 上一个关节速度

        # dof_var = self._robot.data.joint_pos[:,self.left_arm_indices + self.right_arm_indices]   # 关节变量
        # print(dof_pos.shape)
        flag = self.flag.clone()
        flag[self.flag == 0] = 0
        flag[self.flag == 1] = 0
        flag[self.flag == 2] = 0
        flag[self.flag == 3] = 1
        flag[self.flag == 4] = 1
        flag[self.flag == 5] = 1
        # print('time',time.time()-start_time)       
        # print('Object_poses',Object_poses)
        # Object_poses = Object_poses * 10
        point_var = torch.zeros((self.num_envs,800*3),device=self.device,dtype=torch.float32)
        dof_var = torch.zeros((self.num_envs,22),device=self.device,dtype=torch.float32)


        right_var = torch.concat((cur_right_joint_p, cur_right_joint_v, End_pos_quat[:,7:]),dim=1)
        left_var = torch.concat((cur_left_joint_p, cur_left_joint_v, End_pos_quat[:,:7]),dim=1)
        # flag_var = flag.unsqueeze(-1).type_as(dof_var)
        right_flag_var = torch.tensor([[0]],device=self.device,dtype=torch.float32).repeat(self.num_envs,1)
        left_flag_var = torch.tensor([[1]],device=self.device,dtype=torch.float32).repeat(self.num_envs,1)

        dof_var[flag==0,0:1] = right_flag_var[flag==0]
        dof_var[flag==0,1:] = right_var[flag==0]
        dof_var[flag==1,0:1] = left_flag_var[flag==1]
        dof_var[flag==1,1:] = left_var[flag==1]
        # print('dof_var',dof_var)

        # points_aoi_left = points_aoi_left.reshape((self.num_envs,800*3))
        # points_aoi_right = points_aoi_right.reshape((self.num_envs,800*3))
        # point_var[flag==0,:] = points_aoi_right[flag==0]
        # point_var[flag==1,:] = points_aoi_left[flag==1]

        point_var[flag==0,:] = self.target_decect_data[flag==0,800*3:]
        point_var[flag==1,:] = self.target_decect_data[flag==1,:800*3]

        # dof_var = torch.concat((cur_left_joint_p, cur_left_joint_v, End_pos_quat[:,:7], Object_poses_Norm[:,:3],Object_poses[:,:3],
        #                         cur_right_joint_p, cur_right_joint_v,End_pos_quat[:,7:], Object_poses_Norm[:,3:],Object_poses[:,3:]),dim=1) 
        # print('cur_left_joint_p',cur_left_joint_p)
        # print('cur_right_joint_p',cur_right_joint_p)
        # print('cur_left_joint_v',cur_left_joint_v)
        # print('cur_right_joint_v',cur_right_joint_v)
        # print('left_link7_state',left_link7_state)
        # print('right_link7_state',right_link7_state)
        # flag_var2 = flag.unsqueeze(-1).unsqueeze(-1).expand(-1,500,1)
        # points_aoi = torch.cat((flag_var2, points_aoi),dim=-1)

        # import pyvista as pv
        # points = point_var[0].reshape(-1,3).cpu().numpy()
        # print(np.max(points,axis=0))
        # print(np.min(points,axis=0))
        # # 创建 PyVista 点云对象
        # cloud = pv.PolyData(points)
        # # 可视化
        # plotter = pv.Plotter()
        # plotter.add_points(cloud, color='blue', point_size=5)
        # # 添加带有坐标轴刻度的边界框
        # plotter.add_bounding_box(color='black')
        # plotter.show_grid(
        #     grid=True,            # 显示网格
        #     xlabel='X Axis',      # X 轴标签
        #     ylabel='Y Axis',      # Y 轴标签
        #     zlabel='Z Axis',      # Z 轴标签
        #     ticks='outside',      # 刻度位置
        #     font_size=16,         # 字体大小
        #     color='black'         # 颜色
        # )
        # plotter.show()

        # import carb
        # color=(0.6, 1.0, 0.6, 1)
        # size=5.0
        # pt = point_var[0].clone().cpu().reshape(-1,3)
        # angle = -math.pi / 2  # -90 degrees in radians
        # cos_a = math.cos(angle)
        # sin_a = math.sin(angle)
        # Rz = torch.tensor([
        #     [cos_a, -sin_a, 0.0],
        #     [sin_a,  cos_a, 0.0],
        #     [0.0,    0.0,   1.0]
        # ], dtype=torch.float32)
        # t = None  # 无需平移
        # pt = pt @ Rz.T  # 旋转
        # pt[:,2] = pt[:,2] + 1.0
        # points = [carb.Float3(p[0], p[1], p[2]) for p in pt.tolist()]
        # colors = [carb.ColorRgba(*color) for _ in range(len(points))]
        # sizes = [size] * len(points)
        # self.draw.clear_points()  # 清除上一次
        # self.draw.draw_points(points, colors, sizes)

        obs = torch.cat((dof_var, point_var),dim=-1)

        # print('obs.shape',obs.shape)

        observations = {"policy": obs.clone()}

        # if self.cfg.write_image_to_file:
        #     save_images_to_file(observations["policy"], f"cartpole_{data_type}.png")
        # print('time ', time.time()-a)
        return observations

    def quaternion_to_rotation_matrix(self,q):
        """
        将四元数转换为旋转矩阵
        :param q: 四元数张量，形状为 (N, 4)
        :return: 旋转矩阵张量，形状为 (N, 3, 3)
        """
        N = q.shape[0]
        w = q[:, 0]
        x = q[:, 1]
        y = q[:, 2]
        z = q[:, 3]

        R = torch.stack([
            1 - 2 * y**2 - 2 * z**2, 2 * x * y - 2 * z * w, 2 * x * z + 2 * y * w,
            2 * x * y + 2 * z * w, 1 - 2 * x**2 - 2 * z**2, 2 * y * z - 2 * x * w,
            2 * x * z - 2 * y * w, 2 * y * z + 2 * x * w, 1 - 2 * x**2 - 2 * y**2
        ], dim=1).view(N, 3, 3)
        return R

    # def calculate_deflection_angle(self, qa, qb):
    #     """
    #     计算坐标系 A 的 y 轴与坐标系 B 的 z 轴在世界坐标系下的偏转角
    #     :param qa: 坐标系 A 相对于世界坐标系的四元数张量，形状为 (N, 4)
    #     :param qb: 坐标系 B 相对于世界坐标系的四元数张量，形状为 (N, 4)
    #     :return: 偏转角张量（弧度），形状为 (N, 1)
    #     """
    #     # 四元数转旋转矩阵
    #     R_a = self.quaternion_to_rotation_matrix(qa)  # (N, 3, 3)
    #     R_b = self.quaternion_to_rotation_matrix(qb)  # (N, 3, 3)

    #     # A 的 y 轴在世界坐标系中（第 1 列）
    #     y_axis_A_world = R_a[:, :, 1]  # (N, 3)

    #     # B 的 z 轴在世界坐标系中（第 2 列）
    #     z_axis_B_world = R_b[:, :, 2]  # (N, 3)

    #     # 点积
    #     dot_product = torch.sum(y_axis_A_world * z_axis_B_world, dim=1)

    #     # 各向量模长
    #     norm_y = torch.norm(y_axis_A_world, dim=1)
    #     norm_z = torch.norm(z_axis_B_world, dim=1)

    #     # 余弦值，裁剪数值范围防止 acos 出错
    #     cos_angle = torch.clamp(dot_product / (norm_y * norm_z), -1.0, 1.0)

    #     # 弧度夹角
    #     angle = torch.acos(cos_angle).unsqueeze(1)

    #     return angle
    
    def calculate_deflection_angle(self,q):
        """
        计算坐标系 A 的 y 轴与世界坐标系 B 的 z 轴的偏转角
        :param q: 坐标系 A 相对于坐标系 B 的四元数张量，形状为 (N, 4)
        :return: 偏转角张量，形状为 (N, 1)
        """
        # 将四元数转换为旋转矩阵
        R = self.quaternion_to_rotation_matrix(q)

        # 坐标系 A 的 y 轴在世界坐标系中的表示
        y_axis_A_in_B = R[:, :, 1]

        # 世界坐标系的 z 轴
        z_axis_B = torch.tensor([0, 0, 1], dtype=torch.float32,device = self.device).unsqueeze(0).expand(q.shape[0], -1)

        # 计算两个向量的点积
        dot_product = torch.sum(y_axis_A_in_B * z_axis_B, dim=1)

        # 计算两个向量的模长
        norm_y_axis_A_in_B = torch.norm(y_axis_A_in_B, dim=1)
        norm_z_axis_B = torch.norm(z_axis_B, dim=1)

        # 计算夹角的余弦值
        cos_angle = dot_product / (norm_y_axis_A_in_B * norm_z_axis_B)

        # 计算夹角（弧度）
        angle = torch.acos(cos_angle).unsqueeze(1)

        return angle




