
# Copyright (c) 2022-2025, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

import torch
torch.cuda.empty_cache()

from isaacsim.core.utils.stage import get_current_stage
from isaacsim.core.utils.torch.transformations import tf_combine, tf_inverse, tf_vector
from pxr import UsdGeom

import isaaclab.sim as sim_utils
from isaaclab.actuators.actuator_cfg import ImplicitActuatorCfg
from isaaclab.assets import Articulation, ArticulationCfg, AssetBaseCfg, AssetBase, RigidObjectCfg, RigidObject
from isaaclab.envs import DirectRLEnv, DirectRLEnvCfg
from isaaclab.scene import InteractiveSceneCfg
from isaaclab.sim import SimulationCfg
from isaaclab.terrains import TerrainImporterCfg
from isaaclab.utils import configclass
from isaaclab.utils.assets import ISAAC_NUCLEUS_DIR
from isaaclab.utils.math import sample_uniform
from isaaclab.sensors import TiledCameraCfg,TiledCamera,ContactSensorCfg, ContactSensor
import isaaclab.utils.math as math_utils
import math

from .ObjectCfg import Table_CFG, Kettle_CFG, Cup_CFG, QiruiRobot_CFG, Camera_CFG
from copy import deepcopy

# 环境配置类
@configclass
class QiruiEnvCfg(DirectRLEnvCfg):
    # Environment Configuration
    episode_length_s: float = 10.0    # 迭代长度
    decimation: int = 4          
    action_space: int = 7 * 2          #  动作空间大小
    observation_space: int = 7 * 2 + 4 * 256 * 256          # 观测空间大小
    state_space: int = 0  # Corrected to 0 to avoid negative dimensions
    debug_vis: bool = True         # 是否可显示

    # Simulation Configuration
    sim: SimulationCfg = SimulationCfg(
        dt=1 / 150,
        render_interval=decimation,
        #disable_contact_processing=True,
        physics_material=sim_utils.RigidBodyMaterialCfg(
            friction_combine_mode="multiply",
            restitution_combine_mode="multiply",
            static_friction=1.0,
            dynamic_friction=1.0,
            restitution=0.0,
        ),
    )

    # Scene Configuration
    scene: InteractiveSceneCfg = InteractiveSceneCfg(num_envs=15, env_spacing=100, replicate_physics=True)  

    # Robot Configuration

    # articulation
    Qiruirobot: ArticulationCfg = QiruiRobot_CFG.replace(prim_path="/World/envs/env_.*/QiruiRobot")   # 机器人配置

    Table = Table_CFG.replace(prim_path="/World/envs/env_.*/Table")   # 桌子配置
 
    Kettle: RigidObjectCfg = Kettle_CFG.replace(prim_path="/World/envs/env_.*/Kettle")    # 水壶配置

    Cup: RigidObjectCfg = Cup_CFG.replace(prim_path="/World/envs/env_.*/Cup")      # 水杯配置

    camera: TiledCameraCfg = Camera_CFG.replace(prim_path="/World/envs/env_.*/QiruiRobot/front_cam")     # 相机配置


    # contact_forces_left_hand_proximal = ContactSensorCfg(
    #     prim_path="/World/envs/env_.*/Cup/cup", update_period=0.0, history_length=6, debug_vis=True, filter_prim_paths_expr="/World/envs/env_.*/QiruiRobot/left_hand_.*_proximal",
    # )   # 接触传感器配置（左手）

    # contact_forces_right_hand_proximal = ContactSensorCfg(
    #     prim_path="/World/envs/env_.*/Kettle/kettle", update_period=0.0, history_length=6, debug_vis=True, filter_prim_paths_expr="/World/envs/env_.*/QiruiRobot/right_hand_.*_proximal",
    # )   # 接触传感器配置（右手）

    # contact_sensor_table: ContactSensorCfg = ContactSensorCfg(
    #     prim_path=f"/World/envs/env_.*/Table/table", update_period=0.0, history_length=6, debug_vis=True, filter_prim_paths_expr= "/World/envs/env_.*/QiruiRobot/.*(hand|wrist|arm).*",
    # )

    action_scale =  1    # 动作的最大量

    # thrust_scale: float = 10.0  
    # angle_limit: float = math.pi / 4  

    # Reward Scales
    # ang_vel_reward_scale: float = -0.05
    # distance_to_goal_reward_scale: float = -0.1
    # orientation_penalty_scale: float = -0.1
    # joint_limit_penalty_scale: float = -1




class QiruiEnv(DirectRLEnv):
    # pre-physics step calls
    #   |-- _pre_physics_step(action)
    #   |-- _apply_action()
    # post-physics step calls
    #   |-- _get_dones()
    #   |-- _get_rewards()
    #   |-- _reset_idx(env_ids)
    #   |-- _get_observations()

    cfg: QiruiEnvCfg

    def __init__(self, cfg: QiruiEnvCfg, render_mode: str | None = None, **kwargs):
        super().__init__(cfg, render_mode, **kwargs)
        self.left_arm_indices = [2, 6, 10, 14, 18, 22, 24]
        self.right_arm_indices = [3, 7, 11, 15, 19, 23, 25]
        self.left_hand_indices = list(range(26, 31)) + list(range(36, 41)) + [46, 48]
        self.right_hand_indices = list(range(31, 36)) + list(range(41, 46)) + [47, 49]
        self.kettle_orinal_pos = self._kettle.data.root_pos_w    # 水杯初始位置
        self.kettle_orinal_quat = self._kettle.data.root_quat_w   # 水杯初始姿态

        self.cup_orinal_pos = self._cup.data.root_pos_w    # 水壶初始位置
        self.cup_orinal_quat = self._cup.data.root_quat_w  # 水壶初始姿态

        self.table_orinal_pos = self._table.data.root_pos_w    # 桌子初始位置
        self.table_orinal_quat = self._table.data.root_quat_w  # 桌子初始姿态
         
        self.left_hand_base_link_idx = self._robot.find_bodies("left_hand_base_link")[0][0]    # 左手掌的索引
        self.right_hand_base_link_idx = self._robot.find_bodies("right_hand_base_link")[0][0]   # 右手掌的索引

        kettle_pos = self._kettle.data.root_com_pos_w      # 水壶中心位置
        kettle_pos[:,0] = kettle_pos[:,0] - 0.164 
        kettle_pos[:,2] = kettle_pos[:,2] + 0.04          # 设置偏置
        cup_pos = self._cup.data.root_com_pos_w      # 水杯中心位置
        cup_pos[:,0] = cup_pos[:,0] + 0.0684             # 设置偏置
        cup_pos[:,1] = cup_pos[:,1] - 0.005            # 设置偏置

        self.target_left_joint = torch.tensor([0.285578,0.861699,0.729880,-1.642725,0.744932,0.584146,-0.185631],device = self.device)
        self.target_right_joint = torch.tensor([0.383147,-0.410289,0.429334,1.885669,0.416159,-0.457655,-0.112944],device = self.device)
        left_joint  = self._robot.data.joint_pos[:,self.left_arm_indices ]   # 关节变量
        right_joint  = self._robot.data.joint_pos[:,self.right_arm_indices ]   # 关节变量
        self.k_r_d = torch.norm(right_joint - self.target_right_joint, p=2, dim=-1)
        self.c_l_d = torch.norm(left_joint - self.target_left_joint, p=2, dim=-1)

        # self.left_hand_thumb_tip_idx = self._robot.find_bodies("left_hand_thumb_tip")[0][0]
        # self.left_hand_index_tip_idx = self._robot.find_bodies("left_hand_index_tip")[0][0]
        # self.left_hand_middle_tip_idx = self._robot.find_bodies("left_hand_middle_tip")[0][0]
        # self.left_hand_pinky_tip_idx = self._robot.find_bodies("left_hand_pinky_tip")[0][0]
        # self.left_hand_ring_tip_idx = self._robot.find_bodies("left_hand_ring_tip")[0][0]
        # self.right_hand_thumb_tip_idx = self._robot.find_bodies("right_hand_thumb_tip")[0][0]
        # self.right_hand_index_tip_idx = self._robot.find_bodies("right_hand_index_tip")[0][0]
        # self.right_hand_middle_tip_idx = self._robot.find_bodies("right_hand_middle_tip")[0][0]
        # self.right_hand_pinky_tip_idx = self._robot.find_bodies("right_hand_pinky_tip")[0][0]
        # self.right_hand_ring_tip_idx = self._robot.find_bodies("right_hand_ring_tip")[0][0]

        # left_hand_thumb_tip_pos = self._robot.data.body_com_pos_w[:,self.left_hand_thumb_tip_idx]
        # left_hand_index_tip_pos = self._robot.data.body_com_pos_w[:,self.left_hand_index_tip_idx]
        # left_hand_middle_tip_pos = self._robot.data.body_com_pos_w[:,self.left_hand_middle_tip_idx]
        # left_hand_pinky_tip_pos = self._robot.data.body_com_pos_w[:,self.left_hand_pinky_tip_idx]
        # left_hand_ring_tip_pos = self._robot.data.body_com_pos_w[:,self.left_hand_ring_tip_idx]
        # right_hand_thumb_tip_pos = self._robot.data.body_com_pos_w[:,self.right_hand_thumb_tip_idx]
        # right_hand_index_tip_pos = self._robot.data.body_com_pos_w[:,self.right_hand_index_tip_idx]
        # right_hand_middle_tip_pos = self._robot.data.body_com_pos_w[:,self.right_hand_middle_tip_idx]
        # right_hand_ring_tip_pos = self._robot.data.body_com_pos_w[:,self.right_hand_ring_tip_idx]  
        # right_hand_pinky_tip_pos = self._robot.data.body_com_pos_w[:,self.right_hand_pinky_tip_idx]

        # self.kettle_thumb_p = deepcopy(kettle_pos)
        # self.kettle_thumb_p[:,2] = self.kettle_thumb_p[:,2] + 0.02
        # self.kettle_thumb_p[:,1] = self.kettle_thumb_p[:,1] + 0.01
        # self.kettle_thumb_p[:,0] = self.kettle_thumb_p[:,0] + 0.02
        # self.kettle_index_p = deepcopy(kettle_pos)
        # self.kettle_index_p[:,2] = self.kettle_index_p[:,2] + 0.02
        # self.kettle_index_p[:,1] = self.kettle_index_p[:,1] - 0.05
        # self.kettle_middle_p = deepcopy(kettle_pos)
        # self.kettle_middle_p[:,2] = self.kettle_middle_p[:,2] + 0.00
        # self.kettle_middle_p[:,1] = self.kettle_middle_p[:,1] - 0.06
        # self.kettle_ring_p = deepcopy(kettle_pos)
        # self.kettle_ring_p[:,2] = self.kettle_ring_p[:,2] - 0.01
        # self.kettle_ring_p[:,1] = self.kettle_ring_p[:,1] - 0.05
        # self.kettle_pinky_p = deepcopy(kettle_pos)
        # self.kettle_pinky_p[:,2] = self.kettle_pinky_p[:,2] - 0.02
        # self.kettle_pinky_p[:,1] = self.kettle_pinky_p[:,1] - 0.04
        # self.k_r_d = torch.norm(self.kettle_thumb_p - right_hand_thumb_tip_pos, p=2, dim=-1) + torch.norm(self.kettle_index_p - right_hand_index_tip_pos, p=2, dim=-1) + torch.norm(
        # self.kettle_middle_p - right_hand_middle_tip_pos, p=2, dim=-1)+ torch.norm(self.kettle_ring_p - right_hand_ring_tip_pos, p=2, dim=-1) + torch.norm(self.kettle_pinky_p - right_hand_pinky_tip_pos, p=2, dim=-1) 
        

        # self.cup_thumb_p = deepcopy(cup_pos)
        # self.cup_thumb_p[:,2] = self.cup_thumb_p[:,2] + 0.02
        # self.cup_thumb_p[:,1] = self.cup_thumb_p[:,1] + 0.01
        # self.cup_thumb_p[:,0] = self.cup_thumb_p[:,0] - 0.02
        # self.cup_index_p = deepcopy(cup_pos)
        # self.cup_index_p[:,2] = self.cup_index_p[:,2] + 0.02
        # self.cup_index_p[:,1] = self.cup_index_p[:,1] - 0.05
        # self.cup_middle_p = deepcopy(cup_pos)
        # self.cup_middle_p[:,2] = self.cup_middle_p[:,2]
        # self.cup_middle_p[:,1] = self.cup_middle_p[:,1] - 0.06
        # self.cup_ring_p = deepcopy(cup_pos)
        # self.cup_ring_p[:,2] = self.cup_ring_p[:,2] - 0.01
        # self.cup_ring_p[:,1] = self.cup_ring_p[:,1] - 0.05
        # self.cup_pinky_p = deepcopy(cup_pos)
        # self.cup_pinky_p[:,2] = self.cup_pinky_p[:,2] - 0.02
        # self.cup_pinky_p[:,1] = self.cup_pinky_p[:,1] - 0.04
        # self.c_l_d = torch.norm(self.cup_thumb_p - left_hand_thumb_tip_pos, p=2, dim=-1) + torch.norm(self.cup_index_p - left_hand_index_tip_pos, p=2, dim=-1) + torch.norm(
        # self.cup_middle_p - left_hand_middle_tip_pos, p=2, dim=-1)+ torch.norm(self.cup_ring_p - left_hand_ring_tip_pos, p=2, dim=-1) + torch.norm(self.cup_pinky_p - left_hand_pinky_tip_pos, p=2, dim=-1) 


        # target_rot_l = torch.tensor([1.0, 0.0, 0.0, 0.0], device=self.device).repeat(self.num_envs, 1)
        # target_rot_r = torch.tensor([0.0, 0.0, 1.0, 0.0], device=self.device).repeat(self.num_envs, 1)
        # left_hand_base_rot = self._robot.data.body_quat_w[:,self.left_hand_base_link_idx]
        # right_hand_base_rot = self._robot.data.body_quat_w[:,self.right_hand_base_link_idx]

        # quat_diff_l = math_utils.quat_mul(left_hand_base_rot, math_utils.quat_conjugate(target_rot_l))
        # self.rot_dist_l = 2.0 * torch.asin(torch.clamp(torch.norm(quat_diff_l[:, 0:3], p=2, dim=-1), max=1.0))
        # quat_diff_r = math_utils.quat_mul(right_hand_base_rot, math_utils.quat_conjugate(target_rot_r))
        # self.rot_dist_r = 2.0 * torch.asin(torch.clamp(torch.norm(quat_diff_r[:, 0:3], p=2, dim=-1), max=1.0))


        self.action_scale = self.cfg.action_scale    # 动作尺度
        self.robot_dof_lower_limits = self._robot.data.soft_joint_pos_limits[0, :, 0].to(device=self.device)  # 机器人关节限制最小值
        self.robot_dof_upper_limits = self._robot.data.soft_joint_pos_limits[0, :, 1].to(device=self.device)   # 机器人关节限制最大值

        # print('default_joint_pos',self._robot.data.default_joint_pos[0])
        self.robot_dof_targets = torch.zeros((self.num_envs, self._robot.num_joints), device=self.device)     # 机器人关节原始位置
        # 关节索引
        print(self._robot.find_joints('.*'))

        # 手握紧各关节值
        self.grasp_hand = torch.tensor([0.2, 0.2, 0.3, 0.3, 0.3, 0.5, 0.5, 0.6, 0.6, 0.7, 0.4, 0.3], device=self.device) * 5
        # self.grasp_hand_max = torch.tensor([0.2, 0.2, 0.3, 0.3, 0.3, 0.5, 0.5, 0.6, 0.6, 0.7, 0.4, 0.3], device=self.device) * 5
        # self.grasp_hand_min = torch.tensor([0.2, 0.2, 0.3, 0.3, 0.3, 0.5, 0.5, 0.6, 0.6, 0.7, 0.4, 0.3], device=self.device) * 0
        self.grasp_hand_base = torch.tensor([0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1], device=self.device)
        print('robot_dof_lower_limits', self.robot_dof_lower_limits[self.left_arm_indices+self.right_arm_indices])
        print('robot_dof_upper_limits',self.robot_dof_upper_limits[self.left_arm_indices+self.right_arm_indices])


    # 建立场景
    def _setup_scene(self):
        self._robot = Articulation(self.cfg.Qiruirobot)
        self._kettle = RigidObject(self.cfg.Kettle)
        self._table = RigidObject(self.cfg.Table)
        self._cup = RigidObject(self.cfg.Cup)
        self._camera = TiledCamera(self.cfg.camera)
        # self._contact_forces_left_hand_proximal = ContactSensor(self.cfg.contact_forces_left_hand_proximal)
        # self._contact_forces_right_hand_proximal = ContactSensor(self.cfg.contact_forces_right_hand_proximal)
        # self._contact_sensor_table = ContactSensor(self.cfg.contact_sensor_table)
        # self._contact_forces_left_hand_index_proximal= ContactSensor(self.cfg.contact_forces_left_hand_index_proximal)
        # self._contact_forces_left_hand_middle_proximal= ContactSensor(self.cfg.contact_forces_left_hand_middle_proximal)
        # self._contact_forces_left_hand_pinky_proximal= ContactSensor(self.cfg.contact_forces_left_hand_pinky_proximal)
        # self._contact_forces_left_hand_ring_proximal= ContactSensor(self.cfg.contact_forces_left_hand_ring_proximal)
        # self._contact_forces_left_hand_thumb_proximal= ContactSensor(self.cfg.contact_forces_left_hand_thumb_proximal)

        # self._contact_forces_right_hand_index_proximal= ContactSensor(self.cfg.contact_forces_right_hand_index_proximal)
        # self._contact_forces_right_hand_middle_proximal= ContactSensor(self.cfg.contact_forces_right_hand_middle_proximal)
        # self._contact_forces_right_hand_pinky_proximal= ContactSensor(self.cfg.contact_forces_right_hand_pinky_proximal)
        # self._contact_forces_right_hand_ring_proximal= ContactSensor(self.cfg.contact_forces_right_hand_ring_proximal)
        # self._contact_forces_right_hand_thumb_proximal= ContactSensor(self.cfg.contact_forces_right_hand_thumb_proximal)
        
        self.scene.articulations["robot"] = self._robot
        self.scene.rigid_objects["kettle"] = self._kettle
        self.scene.rigid_objects["table"] = self._table
        self.scene.rigid_objects["cup"] = self._cup
        self.scene.sensors["camera"] = self._camera
        # self.scene.sensors["contact_forces_left_hand_proximal"] = self._contact_forces_left_hand_proximal
        # self.scene.sensors["contact_forces_right_hand_proximal"] = self._contact_forces_right_hand_proximal
        # self.scene.sensors["contact_sensor_table"] = self._contact_sensor_table

        # self.scene.sensors["contact_forces_left_hand_index_proximal"] = self._contact_forces_left_hand_index_proximal
        # self.scene.sensors["contact_forces_left_hand_middle_proximal"] = self._contact_forces_left_hand_middle_proximal
        # self.scene.sensors["contact_forces_left_hand_pinky_proximal"] = self._contact_forces_left_hand_pinky_proximal
        # self.scene.sensors["contact_forces_left_hand_ring_proximal"] = self._contact_forces_left_hand_ring_proximal
        # self.scene.sensors["contact_forces_left_hand_thumb_proximal"] = self._contact_forces_left_hand_thumb_proximal

        # self.scene.sensors["contact_forces_right_hand_index_proximal"] = self._contact_forces_right_hand_index_proximal
        # self.scene.sensors["contact_forces_right_hand_middle_proximal"] = self._contact_forces_right_hand_middle_proximal
        # self.scene.sensors["contact_forces_right_hand_pinky_proximal"] = self._contact_forces_right_hand_pinky_proximal
        # self.scene.sensors["contact_forces_right_hand_ring_proximal"] = self._contact_forces_right_hand_ring_proximal
        # self.scene.sensors["contact_forces_right_hand_thumb_proximal"] = self._contact_forces_right_hand_thumb_proximal

        # self.cfg.terrain.num_envs = self.scene.cfg.num_envs
        # self.cfg.terrain.env_spacing = self.scene.cfg.env_spacing
        # self._terrain = self.cfg.terrain.class_type(self.cfg.terrain)

        # clone, filter, and replicate
        self.scene.clone_environments(copy_from_source=False)
        self.scene.filter_collisions(global_prim_paths=[])

        # add lights
        light_cfg = sim_utils.DomeLightCfg(intensity=2000.0, color=(0.75, 0.75, 0.75))
        light_cfg.func("/World/Light", light_cfg)

        cfg_ground = sim_utils.GroundPlaneCfg(size=(2000,2000))
        cfg_ground.func("/World/defaultGroundPlane", cfg_ground)

    # pre-physics step calls
    # 手执行抓取和放开动作
    def _grasp(self, left_grasp_enable, right_grasp_enable):
        #手张开
        joint_lhand_loose = self._robot.data.soft_joint_pos_limits[:, self.left_hand_indices, 0]
        self.robot_dof_targets[:, self.left_hand_indices] = joint_lhand_loose
        joint_rhand_loose = self._robot.data.soft_joint_pos_limits[:, self.right_hand_indices, 0]
        self.robot_dof_targets[:, self.right_hand_indices] = joint_rhand_loose

        #左手抓握
        left_hand_joint = self.robot_dof_targets[left_grasp_enable, :]
        left_hand_joint[:, self.left_hand_indices] = self.grasp_hand
        self.robot_dof_targets[left_grasp_enable, :] = left_hand_joint
        #右手抓握
        right_hand_joint = self.robot_dof_targets[right_grasp_enable, :]
        right_hand_joint[:, self.right_hand_indices] = self.grasp_hand
        self.robot_dof_targets[right_grasp_enable, :] = right_hand_joint

    # 动作前的函数
    def _pre_physics_step(self, actions: torch.Tensor):
        # 复位手的位置
        # left_grasp_enable = torch.zeros((self.num_envs), device=self.device, dtype=torch.bool)   
        # right_grasp_enable = torch.zeros((self.num_envs), device=self.device, dtype=torch.bool)
        # self._grasp(left_grasp_enable, right_grasp_enable)

        # term_left_hand = torch.flatten(self.c_l_d < 0.1)
        # term_right_hand = torch.flatten(self.k_r_d < 0.1)
        # self._grasp(term_left_hand, term_right_hand)  # 如果到达指定位置（距离小于0.1）则手抓紧、
        # print('actions',actions)
        # 左臂右臂关节值赋值
        self.actions = actions.clone().clamp(-1.0, 1.0)
        self.robot_dof_targets = self._robot.data.joint_pos
        # self.robot_dof_targets[:, self.left_hand_indices] = self.robot_dof_targets[:,self.left_hand_indices] + self.actions[:,14:15] * self.grasp_hand_base
        # self.robot_dof_targets[:, self.right_hand_indices] = self.robot_dof_targets[:,self.right_hand_indices] + self.actions[:,15:16] * self.grasp_hand_base
        self.robot_dof_targets[:, self.left_arm_indices + self.right_arm_indices] = self.robot_dof_targets[:,self.left_arm_indices+self.right_arm_indices] + self.actions[:,:14] * self.action_scale   # 给左臂和右臂赋值
        targets = self.robot_dof_targets
        self.robot_dof_targets[:] = torch.clamp(targets, self.robot_dof_lower_limits, self.robot_dof_upper_limits)
    
    # 执行动作
    def _apply_action(self):
        # 驱动关节
        self._robot.set_joint_position_target(self.robot_dof_targets)

    # post-physics step calls
    # 判断回合有没有结束
    def _get_dones(self) -> tuple[torch.Tensor, torch.Tensor]:
        left_joint  = self._robot.data.joint_pos[:,self.left_arm_indices ]   # 关节变量
        right_joint  = self._robot.data.joint_pos[:,self.right_arm_indices ]   # 关节变量
        self.k_r_d_cur = torch.norm(right_joint - self.target_right_joint, p=2, dim=-1)
        self.c_l_d_cur = torch.norm(left_joint - self.target_left_joint, p=2, dim=-1)

        # left_hand_thumb_tip_pos = self._robot.data.body_com_pos_w[:,self.left_hand_thumb_tip_idx]
        # left_hand_index_tip_pos = self._robot.data.body_com_pos_w[:,self.left_hand_index_tip_idx]
        # left_hand_middle_tip_pos = self._robot.data.body_com_pos_w[:,self.left_hand_middle_tip_idx]
        # left_hand_pinky_tip_pos = self._robot.data.body_com_pos_w[:,self.left_hand_pinky_tip_idx]
        # left_hand_ring_tip_pos = self._robot.data.body_com_pos_w[:,self.left_hand_ring_tip_idx]
        # right_hand_thumb_tip_pos = self._robot.data.body_com_pos_w[:,self.right_hand_thumb_tip_idx]
        # right_hand_index_tip_pos = self._robot.data.body_com_pos_w[:,self.right_hand_index_tip_idx]
        # right_hand_middle_tip_pos = self._robot.data.body_com_pos_w[:,self.right_hand_middle_tip_idx]
        # right_hand_pinky_tip_pos = self._robot.data.body_com_pos_w[:,self.right_hand_pinky_tip_idx]
        # right_hand_ring_tip_pos = self._robot.data.body_com_pos_w[:,self.right_hand_ring_tip_idx]


        #         # distance from hand to the drawer
        # self.k_r_d_cur = torch.norm(self.kettle_thumb_p - right_hand_thumb_tip_pos, p=2, dim=-1) + torch.norm(self.kettle_index_p - right_hand_index_tip_pos, p=2, dim=-1) + torch.norm(
        # self.kettle_middle_p - right_hand_middle_tip_pos, p=2, dim=-1)+ torch.norm(self.kettle_ring_p - right_hand_ring_tip_pos, p=2, dim=-1) + torch.norm(self.kettle_pinky_p - right_hand_pinky_tip_pos, p=2, dim=-1) 
        # self.c_l_d_cur = torch.norm(self.cup_thumb_p - left_hand_thumb_tip_pos, p=2, dim=-1) + torch.norm(self.cup_index_p - left_hand_index_tip_pos, p=2, dim=-1) + torch.norm(
        # self.cup_middle_p - left_hand_middle_tip_pos, p=2, dim=-1)+ torch.norm(self.cup_ring_p - left_hand_ring_tip_pos, p=2, dim=-1) + torch.norm(self.cup_pinky_p - left_hand_pinky_tip_pos, p=2, dim=-1) 
        self.term_left_hand = torch.flatten(self.k_r_d_cur < 0.03)    # 判断是否到达
        self.term_right_hand = torch.flatten(self.c_l_d_cur < 0.03)
        term_left_hand = self.term_left_hand.unsqueeze(1)
        term_right_hand = self.term_right_hand.unsqueeze(1)

        # 抓取成功否判断
        # threthod_force = 2
        # term_left_hand_grasp1 = torch.any(torch.abs(self._contact_forces_left_hand_index_proximal.data.force_matrix_w).squeeze(dim=1) > threthod_force, dim=(1,2)).unsqueeze(1)
        # term_left_hand_grasp2 = torch.any(torch.abs(self._contact_forces_left_hand_middle_proximal.data.force_matrix_w).squeeze(dim=1) > threthod_force, dim=(1,2)).unsqueeze(1)
        # term_left_hand_grasp3 = torch.any(torch.abs(self._contact_forces_left_hand_pinky_proximal.data.force_matrix_w).squeeze(dim=1) > threthod_force, dim=(1,2)).unsqueeze(1)
        # term_left_hand_grasp4 = torch.any(torch.abs(self._contact_forces_left_hand_ring_proximal.data.force_matrix_w).squeeze(dim=1) > threthod_force, dim=(1,2)).unsqueeze(1)
        # term_left_hand_grasp5 = torch.any(torch.abs(self._contact_forces_left_hand_thumb_proximal.data.force_matrix_w).squeeze(dim=1) > threthod_force, dim=(1,2)).unsqueeze(1)
        # self.term_left_hand_grasp = torch.all(torch.concat((term_left_hand_grasp1,term_left_hand_grasp2,term_left_hand_grasp3,term_left_hand_grasp4,term_left_hand_grasp5),dim=1),dim=1)
        # # term_left_hand_grasp = torch.any(torch.abs(self._contact_forces_left_hand_proximal.data.force_matrix_w).squeeze(dim=1) > threthod_force, dim=(1,2))
        # # term_right_hand_grasp = torch.any(torch.abs(self._contact_forces_right_hand_proximal.data.force_matrix_w).squeeze(dim=1) > threthod_force, dim=(1,2))
        # term_right_hand_grasp1 = torch.any(torch.abs(self._contact_forces_right_hand_index_proximal.data.force_matrix_w).squeeze(dim=1) > threthod_force, dim=(1,2)).unsqueeze(1)
        # term_right_hand_grasp2 = torch.any(torch.abs(self._contact_forces_right_hand_middle_proximal.data.force_matrix_w).squeeze(dim=1) > threthod_force, dim=(1,2)).unsqueeze(1)
        # term_right_hand_grasp3 = torch.any(torch.abs(self._contact_forces_right_hand_pinky_proximal.data.force_matrix_w).squeeze(dim=1) > threthod_force, dim=(1,2)).unsqueeze(1)
        # term_right_hand_grasp4 = torch.any(torch.abs(self._contact_forces_right_hand_ring_proximal.data.force_matrix_w).squeeze(dim=1) > threthod_force, dim=(1,2)).unsqueeze(1)
        # term_right_hand_grasp5 = torch.any(torch.abs(self._contact_forces_right_hand_thumb_proximal.data.force_matrix_w).squeeze(dim=1) > threthod_force, dim=(1,2)).unsqueeze(1)
        # self.term_right_hand_grasp = torch.all(torch.concat((term_right_hand_grasp1,term_right_hand_grasp2,term_right_hand_grasp3,term_right_hand_grasp4,term_right_hand_grasp5),dim=1),dim=1)
        # term_left_hand_grasp = self.term_left_hand_grasp.unsqueeze(1)
        # term_right_hand_grasp = self.term_right_hand_grasp.unsqueeze(1)
        
        # 杯子、水壶以及桌子是否挪动
        kettle_root_pos = self._kettle.data.root_pos_w
        cup_root_pos = self._cup.data.root_pos_w
        self.k_change = torch.any(torch.abs(kettle_root_pos - self.kettle_orinal_pos) > 0.1, dim=1)
        self.c_change = torch.any(torch.abs(cup_root_pos - self.cup_orinal_pos) > 0.1, dim=1)
        # k_fall = kettle_pos[:,2] < 0.1
        # c_fall = cup_pos[:,2] < 0.1
        k_change = self.k_change.unsqueeze(1)
        c_change = self.c_change.unsqueeze(1)


        table_root_pos = self._table.data.root_pos_w
        self.term_arm_hand = torch.any(torch.abs(table_root_pos - self.table_orinal_pos) > 0.01, dim=1)
        term_arm_hand = self.term_arm_hand.unsqueeze(1)
        # if torch.any(term_arm_hand):
        #     print('term_arm_hand  ',term_arm_hand)

        terminated = torch.any(torch.concat((term_left_hand, term_right_hand, k_change, c_change,term_arm_hand), dim=1),dim=1)
        # print("terminated",terminated)
        truncated = self.episode_length_buf >= self.max_episode_length - 1
        # print("truncated",truncated)
        return terminated, truncated

    # 奖励函数
    def _get_rewards(self) -> torch.Tensor:

        # left_hand_base_rot = self._robot.data.body_quat_w[:,self.left_hand_base_link_idx]
        # right_hand_base_rot = self._robot.data.body_quat_w[:,self.right_hand_base_link_idx]


    # ------------------------------  approach rewarnd --------------------------------------------------
        # distance from hand to the drawer
        # dist_reward_r = torch.where(self.k_r_d_cur < self.k_r_d, 0.3, -0.3)
        dist_reward_r =  (3.0- self.k_r_d_cur)
        # print("self.k_r_d_cur < self.k_r_d ",self.k_r_d_cur < self.k_r_d )
        # self.k_r_d = self.k_r_d_cur.clone() 
        print("self.k_r_d ",self.k_r_d )
        # print('dist_reward_r',dist_reward_r)
        # dist_reward_l = torch.where(self.c_l_d_cur < self.c_l_d, 0.3, -0.3)
        dist_reward_l =  (3.0- self.c_l_d_cur)
        # self.c_l_d = self.c_l_d_cur.clone() 
        print("c_l_d ", self.c_l_d)
        approach_reward_l = torch.where(self.term_left_hand, 10, 0)
        approach_reward_r = torch.where(self.term_right_hand, 10, 0)

    # ------------------------------  grasp rewarnd --------------------------------------------------
        # grasp_reward_l = torch.where(self.term_left_hand_grasp, 10, 0)
        # grasp_reward_r = torch.where(self.term_right_hand_grasp, 10, 0)

    # ------------------------------  quat rewarnd --------------------------------------------------
        # target_rot_r = torch.tensor([1.0, 0.0, 0.0, 0.0], device=self.device).repeat(self.num_envs, 1)
        # target_rot_l = torch.tensor([0.0, 0.0, 1.0, 0.0], device=self.device).repeat(self.num_envs, 1)
        # quat_diff_l = math_utils.quat_mul(left_hand_base_rot, math_utils.quat_conjugate(target_rot_l))
        # rot_dist_l = 2.0 * torch.asin(torch.clamp(torch.norm(quat_diff_l[:, 0:3], p=2, dim=-1), max=1.0))
        # rot_reward_l = torch.where(rot_dist_l < self.rot_dist_l, 0.1, 0)
        # self.rot_dist_l = rot_dist_l
        # quat_diff_r = math_utils.quat_mul(right_hand_base_rot, math_utils.quat_conjugate(target_rot_r))
        # rot_dist_r = 2.0 * torch.asin(torch.clamp(torch.norm(quat_diff_r[:, 0:3], p=2, dim=-1), max=1.0))
        # rot_reward_r = torch.where(rot_dist_r < self.rot_dist_r, 0.1, 0)
        # self.rot_dist_r = rot_dist_r
        # print("rot_dist_l",rot_dist_l)

    # ------------------------------  penalty   --------------------------------------------------
        term_arm_hand_penalty = torch.where(self.term_arm_hand, -1, 0)
        k_change_penalty = torch.where(self.k_change, -0.5, 0)
        c_change_penalty = torch.where(self.c_change, -0.5, 0)
   
    # ------------------------------  Final reward   --------------------------------------------------
        rewards = dist_reward_r +  dist_reward_l + approach_reward_l +  approach_reward_r + k_change_penalty + c_change_penalty + term_arm_hand_penalty  
        #  + 0.5 * rot_reward_l + 0.5 * rot_reward_r + approach_reward_l +  approach_reward_r
 
        return rewards

    # 复原函数
    def _reset_idx(self, env_ids: torch.Tensor | None):
        if env_ids is None:
            env_ids = self._robot._ALL_INDICES
        super()._reset_idx(env_ids)
        # robot state
        # joint_pos = self._robot.data.default_joint_pos[env_ids]
        joint_target_pos = torch.zeros((self.num_envs, self._robot.num_joints), device=self.device) 
        joint_pos = joint_target_pos[env_ids,:]

        joint_pos = torch.clamp(joint_pos, self.robot_dof_lower_limits, self.robot_dof_upper_limits)
        
        joint_vel = torch.zeros_like(joint_pos)
        self._robot.set_joint_position_target(joint_pos, env_ids=env_ids)
        self._robot.write_joint_state_to_sim(joint_pos, joint_vel, env_ids=env_ids)

        self._kettle.write_root_pose_to_sim(torch.cat((self.kettle_orinal_pos[env_ids,:],self.kettle_orinal_quat[env_ids,:]),dim=-1), env_ids)
        self._cup.write_root_pose_to_sim(torch.cat((self.cup_orinal_pos[env_ids,:],self.cup_orinal_quat[env_ids,:]),dim=-1),env_ids)
        self._table.write_root_pose_to_sim(torch.cat((self.table_orinal_pos[env_ids,:],self.table_orinal_quat[env_ids,:]),dim=-1),env_ids)

        left_joint  = self._robot.data.joint_pos[:,self.left_arm_indices ]   # 关节变量
        right_joint  = self._robot.data.joint_pos[:,self.right_arm_indices ]   # 关节变量
        self.k_r_d[env_ids] = torch.norm(right_joint[env_ids,:] - self.target_right_joint, p=2, dim=-1)
        self.c_l_d[env_ids] = torch.norm(left_joint[env_ids,:] - self.target_left_joint, p=2, dim=-1)
        # self._contact_forces_left_hand_proximal.reset()
        # self._contact_forces_right_hand_proximal.reset()
        # self._contact_sensor_table.reset()

    # 获取观测值函数
    def _get_observations(self) -> dict:

        camera_data_rgb = self._camera.data.output["rgb"] / 255.0
        # normalize the camera data for better training results
        mean_tensor = torch.mean(camera_data_rgb, dim=(1, 2), keepdim=True)
        camera_data_rgb -= mean_tensor

        camera_data_depth = self._camera.data.output["depth"]
        camera_data_depth[camera_data_depth > float(2.0)] = 0
        # print('camera_data_depth',camera_data_depth.max())
        # max_tensor = torch.max(camera_data_depth,dim=(1,2))
        camera_data_depth = camera_data_depth/2.0

        camera_data = torch.cat((camera_data_rgb,camera_data_depth),dim = -1)  # 图像数据

        dof_pos = self._robot.data.joint_pos[:,self.right_arm_indices + self.left_arm_indices ]   # 关节变量
        # print(dof_pos.shape)

        obs = torch.cat(
            (torch.reshape(camera_data,(-1,256*256*4)),
             dof_pos
             ),
            dim=-1
        )

        observations = {"policy": obs.clone()}

        # if self.cfg.write_image_to_file:
        #     save_images_to_file(observations["policy"], f"cartpole_{data_type}.png")

        return observations



