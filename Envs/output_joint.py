# 检查每个关节是否在限制范围内
def check_joint_limits(joint_result, joint_limits_rad):
    for i, (angle, (lower, upper)) in enumerate(zip(joint_result, joint_limits_rad)):
        if not (lower <= angle <= upper):
            print(f"Joint {i+1} out of limits: {(angle):.2f}° not in [{(lower):.2f}°, {(upper):.2f}°]")
            return False
    return True
def solve_inverse_kinematics2(chain, target_position, target_orientation, joint_init=None, eps=1E-5, maxiter=500, eps_joints=1E-15):
    """
    使用 KDL 求解链的逆运动学。

    Parameters:
        chain (kdl.Chain): 机械臂的 KDL 链。
        target_position (tuple): 目标位置 (x, y, z)。
        target_orientation (tuple): 目标方向的四元数 (x, y, z, w)。
        joint_init (kdl.JntArray): 初始关节位置，默认为 0。若未提供, 则自动初始化为 0。
        eps (float): 逆运动学求解的容差。
        maxiter (int): 最大迭代次数。
        eps_joints (float): 关节精度容差。

    Returns:
        tuple: (success, joint_result)，其中：
            - success (bool): 是否成功求解。
            - joint_result (kdl.JntArray): 求解得到的关节位置。
    """
    # 定义逆运动学求解器
    ik_solver = kdl.ChainIkSolverPos_LMA(chain, eps, maxiter, eps_joints)

    # 初始化关节位置
    num_joints = chain.getNrOfJoints()
    if joint_init is None:
        joint_init = kdl.JntArray(num_joints)
        for i in range(num_joints):
            joint_init[i] = 0.0
    joint_init[3] = 1
    # 定义目标末端执行器位姿
    target_pose = kdl.Frame()
    target_pose.p = kdl.Vector(*target_position)
    target_pose.M = kdl.Rotation.Quaternion(*target_orientation)

    # 初始化结果关节位置
    joint_result = kdl.JntArray(num_joints)

    # 求解逆运动学
    ret = ik_solver.CartToJnt(joint_init, target_pose, joint_result)
    
    if ret != 0:
        print("逆运动学求解失败!")
        exit(0)


    # 设置每个关节的限制（单位：弧度）
    joint_limits = [
        (-170, 170),  # Joint 1 限制
        (-95, 30),  # Joint 2 限制
        (-170, 170),              # Joint 3 限制
        (0, 120),          # Joint 4 限制
        (-170, 170),              # Joint 5 限制
        (-45, 45),  # Joint 6 限制
        (-30, 30)           # Joint 7 限制
    ]   

    joint_result_degrees = [0,0,0,0,0,0,0] 
    for i in range(num_joints):
        joint_result_degrees[i] = joint_result[i] * (180 / 3.1415926)
    # 调用函数检查结果
    if not check_joint_limits(joint_result_degrees, joint_limits):
        exit(0)      

    # 返回结果
    return ret, joint_result

def solve_inverse_kinematics(chain, target_position, target_orientation, joint_init=None, eps=1E-5, maxiter=500, eps_joints=1E-15):
    """
    使用 KDL 求解链的逆运动学。

    Parameters:
        chain (kdl.Chain): 机械臂的 KDL 链。
        target_position (tuple): 目标位置 (x, y, z)。
        target_orientation (tuple): 目标方向的四元数 (x, y, z, w)。
        joint_init (kdl.JntArray): 初始关节位置，默认为 0。若未提供, 则自动初始化为 0。
        eps (float): 逆运动学求解的容差。
        maxiter (int): 最大迭代次数。
        eps_joints (float): 关节精度容差。

    Returns:
        tuple: (success, joint_result)，其中：
            - success (bool): 是否成功求解。
            - joint_result (kdl.JntArray): 求解得到的关节位置。
    """
    # 定义逆运动学求解器
    ik_solver = kdl.ChainIkSolverPos_LMA(chain, eps, maxiter, eps_joints)

    # 初始化关节位置
    num_joints = chain.getNrOfJoints()
    if joint_init is None:
        joint_init = kdl.JntArray(num_joints)
        for i in range(num_joints):
            joint_init[i] = 0.0
    joint_init[3] = -1
    # 定义目标末端执行器位姿
    target_pose = kdl.Frame()
    target_pose.p = kdl.Vector(*target_position)
    target_pose.M = kdl.Rotation.Quaternion(*target_orientation)

    # 初始化结果关节位置
    joint_result = kdl.JntArray(num_joints)

    # 求解逆运动学
    ret = ik_solver.CartToJnt(joint_init, target_pose, joint_result)
    
    if ret != 0:
        print("逆运动学求解失败!")
        exit(0)


    # 设置每个关节的限制（单位：弧度）
    joint_limits = [
        (-170, 170),  # Joint 1 限制
        (-30, 95),  # Joint 2 限制
        (-170, 170),              # Joint 3 限制
        (-120, 0),          # Joint 4 限制
        (-170, 170),              # Joint 5 限制
        (-45, 45),  # Joint 6 限制
        (-30, 30)           # Joint 7 限制
    ]   

    joint_result_degrees = [0,0,0,0,0,0,0] 
    for i in range(num_joints):
        joint_result_degrees[i] = joint_result[i] * (180 / 3.1415926)
    # 调用函数检查结果
    if not check_joint_limits(joint_result_degrees, joint_limits):
        exit(0)      

    # 返回结果
    return ret, joint_result