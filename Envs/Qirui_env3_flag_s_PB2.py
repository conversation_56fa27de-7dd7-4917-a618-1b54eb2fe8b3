
# Copyright (c) 2022-2025, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause
#==============================================================================未使用逆运动学知识=============================================================================================
#==============================================================================考虑水壶和水杯位置的随机性=======================================================================================
#====================================================================================全程训练======================================================================================
from __future__ import annotations
from PIL import Image
import torch
torch.cuda.empty_cache()
import PyKDL as kdl
from ultralytics import YOLO
import torchvision.transforms as transforms
# from urdf_parser_py.urdf import URDF
# from pykdl_utils.kdl_parser import kdl_tree_from_urdf_model
import numpy as np
from isaacsim.core.utils.stage import get_current_stage
from isaacsim.core.utils.torch.transformations import tf_combine, tf_inverse, tf_vector
from pxr import UsdGeom

import isaaclab.sim as sim_utils
from isaaclab.actuators.actuator_cfg import ImplicitActuatorCfg
from isaaclab.assets import Articulation, ArticulationCfg, AssetBaseCfg, AssetBase, RigidObjectCfg, RigidObject
from isaaclab.envs import DirectRLEnv, DirectRLEnvCfg
from isaaclab.scene import InteractiveSceneCfg
from isaaclab.sim import SimulationCfg
from isaaclab.terrains import TerrainImporterCfg
from isaaclab.utils import configclass
from isaaclab.utils.assets import ISAAC_NUCLEUS_DIR
from isaaclab.utils.math import sample_uniform
from isaaclab.sensors import TiledCameraCfg,TiledCamera,ContactSensorCfg, ContactSensor
import isaaclab.utils.math as math_utils
import math
from isaaclab.utils.math import sample_uniform

from .ObjectCfg import Table_CFG, Kettle_CFG, Cup_CFG, QiruiRobot_CFG, Camera_CFG
from copy import deepcopy
import time

# 环境配置类
@configclass
class QiruiEnvCfg(DirectRLEnvCfg):
    # Environment Configuration
    episode_length_s: float = 15.0    # 迭代长度
    decimation: int = 4          
    action_space: int = 12         #  动作空间大小：双臂运动
    observation_space: int = 3+3 + 3+3 + 7+7 + 7*4    # 观测空间大小：图像（RGBD） + 手臂的关节角度值 + 手臂的关节速度值  + 手臂的末端位置 + Flag指标数
    state_space: int = 0  # Corrected to 0 to avoid negative dimensions
    debug_vis: bool = True         # 是否可显示

    # Simulation Configuration
    sim: SimulationCfg = SimulationCfg(
        dt=1 / 150,
        render_interval=decimation,
        #disable_contact_processing=True,
        physics_material=sim_utils.RigidBodyMaterialCfg(
            friction_combine_mode="multiply",
            restitution_combine_mode="multiply",
            static_friction=1.0,
            dynamic_friction=1.0,
            restitution=0.0,
        ),
    )

    # Scene Configuration
    scene: InteractiveSceneCfg = InteractiveSceneCfg(num_envs=15, env_spacing=3, replicate_physics=True)  

    # Robot Configuration

    # articulation
    Qiruirobot: ArticulationCfg = QiruiRobot_CFG.replace(prim_path="/World/envs/env_.*/QiruiRobot")   # 机器人配置

    Table = Table_CFG.replace(prim_path="/World/envs/env_.*/Table")   # 桌子配置
 
    Kettle: RigidObjectCfg = Kettle_CFG.replace(prim_path="/World/envs/env_.*/Kettle")    # 水壶配置

    Cup: RigidObjectCfg = Cup_CFG.replace(prim_path="/World/envs/env_.*/Cup")      # 水杯配置

    camera: TiledCameraCfg = Camera_CFG.replace(prim_path="/World/envs/env_.*/QiruiRobot/front_cam")     # 相机配置
    
    body_names_cup = ['left_hand_index_proximal', 'left_hand_middle_proximal', 'left_hand_pinky_proximal', 'left_hand_ring_proximal', 'left_hand_thumb_proximal']

    for i in range(len(body_names_cup)):
        body_names_cup[i] = "/World/envs/env_.*/QiruiRobot/" + body_names_cup[i]

    contact_forces_left_hand_intermediate = ContactSensorCfg(
    prim_path="/World/envs/env_.*/Cup/cup", update_period=0.0, history_length=6, debug_vis=True, filter_prim_paths_expr=body_names_cup, 
    )   # 接触传感器配置（左手）

    body_names_kettle = ['right_hand_index_intermediate', 'right_hand_middle_intermediate', 'right_hand_pinky_intermediate', 'right_hand_ring_intermediate', 'right_hand_thumb_intermediate']

    for i in range(len(body_names_kettle)):
        body_names_kettle[i] = "/World/envs/env_.*/QiruiRobot/" + body_names_kettle[i]

    contact_forces_right_hand_intermediate = ContactSensorCfg(
        prim_path="/World/envs/env_.*/Kettle/bottle", update_period=0.0, history_length=6, debug_vis=True, filter_prim_paths_expr=body_names_kettle,
    )   # 接触传感器配置（右手）

    # contact_sensor_table: ContactSensorCfg = ContactSensorCfg(
    #     prim_path=f"/World/envs/env_.*/Table/table", update_period=0.0, history_length=6, debug_vis=True, filter_prim_paths_expr= "/World/envs/env_.*/QiruiRobot/.*(hand|wrist|arm).*",
    # )
    body_names = ['left_arm_link01', 'right_arm_link01', 'left_arm_link02', 'right_arm_link02', 'left_arm_link03', 'right_arm_link03', 'left_arm_link04', 'right_arm_link04', 'left_arm_link05', 'right_arm_link05', 'left_arm_link06', 'left_wrist_motor_A', 'left_wrist_motor_B', 'right_arm_link06', 'right_wrist_motor_A', 'right_wrist_motor_B', 'left_arm_link07', 'left_wrist_rod_A', 'left_wrist_rod_B', 'right_arm_link07', 'right_wrist_rod_A', 'right_wrist_rod_B', 'left_hand_base', 'right_hand_base', 'left_hand_base_link', 'right_hand_base_link', 'left_hand_index_proximal', 'left_hand_middle_proximal', 'left_hand_pinky_proximal', 'left_hand_ring_proximal', 'left_hand_thumb_proximal_base', 'right_hand_index_proximal', 'right_hand_middle_proximal', 'right_hand_pinky_proximal', 'right_hand_ring_proximal', 'right_hand_thumb_proximal_base', 'left_hand_index_intermediate', 'left_hand_middle_intermediate', 'left_hand_pinky_intermediate', 'left_hand_ring_intermediate', 'left_hand_thumb_proximal', 'right_hand_index_intermediate', 'right_hand_middle_intermediate', 'right_hand_pinky_intermediate', 'right_hand_ring_intermediate', 'right_hand_thumb_proximal', 'left_hand_index_tip', 'left_hand_middle_tip', 'left_hand_pinky_tip', 'left_hand_ring_tip', 'left_hand_thumb_intermediate', 'right_hand_index_tip', 'right_hand_middle_tip', 'right_hand_pinky_tip', 'right_hand_ring_tip', 'right_hand_thumb_intermediate', 'left_hand_thumb_distal', 'right_hand_thumb_distal', 'left_hand_thumb_tip', 'right_hand_thumb_tip']

    for i in range(len(body_names)):
        body_names[i] = "/World/envs/env_.*/QiruiRobot/" + body_names[i]

    contact_sensor_table: ContactSensorCfg = ContactSensorCfg(
        prim_path="/World/envs/env_.*/Table/table", update_period=0.0, history_length=6, debug_vis=True, filter_prim_paths_expr= body_names,
    )

    action_scale = 0.02   # 动作的最大量

    # thrust_scale: float = 10.0  
    # angle_limit: float = math.pi / 4  

    # Reward Scales
    # ang_vel_reward_scale: float = -0.05
    # distance_to_goal_reward_scale: float = -0.1
    # orientation_penalty_scale: float = -0.1
    # joint_limit_penalty_scale: float = -1

class QiruiEnv(DirectRLEnv):
    # pre-physics step calls
    #   |-- _pre_physics_step(action)
    #   |-- _apply_action()
    # post-physics step calls
    #   |-- _get_dones()
    #   |-- _get_rewards()
    #   |-- _reset_idx(env_ids)
    #   |-- _get_observations()

    cfg: QiruiEnvCfg

    def __init__(self, cfg: QiruiEnvCfg, render_mode: str | None = None, **kwargs):
        super().__init__(cfg, render_mode, **kwargs)
        self.robot_root_pos = self._robot.data.root_pos_w
        self.left_arm_indices = self._robot.find_joints('.*left_arm_joint.*')[0]
        print(self._robot.find_joints('.*left_arm_joint.*'))
        # self.left_arm_indices = [2, 6, 10, 14, 18, 22, 24]
        self.right_arm_indices = self._robot.find_joints('.*right_arm_joint.*')[0]
        print(self._robot.find_joints('.*right_arm_joint.*'))
        # self.right_arm_indices = [3, 7, 11, 15, 19, 23, 25]
        self.left_hand_indices = self._robot.find_joints('left_hand.*joint')[0]
        print(self._robot.find_joints('left_hand.*joint'))
        # self.left_hand_indices = list(range(26, 31)) + list(range(36, 41)) + [46, 48]
        self.right_hand_indices = self._robot.find_joints('right_hand.*joint')[0]
        # self.right_hand_indices = list(range(31, 36)) + list(range(41, 46)) + [47, 49]

        self.camera_original_pos = self._camera.data.pos_w.clone()

        self.kettle_orinal_pos = self._kettle.data.root_pos_w.clone()    # 水杯初始位置
        self.kettle_orinal_quat = self._kettle.data.root_quat_w.clone()   # 水杯初始姿态
        self.kettle_change_pos = self._kettle.data.root_pos_w.clone()    # 水杯位置

        self.cup_orinal_pos = self._cup.data.root_pos_w.clone()    # 水壶初始位置
        self.cup_orinal_quat = self._cup.data.root_quat_w.clone()  # 水壶初始姿态
        self.cup_change_pos = self._cup.data.root_pos_w.clone()    # 水壶初始位置

        kettle_pos = self._kettle.data.root_com_pos_w.clone()      # 水壶中心位置
        self.kettle_z = kettle_pos[:,2].clone()

        self.table_orinal_pos = self._table.data.root_pos_w.clone()    # 桌子初始位置
        self.table_orinal_quat = self._table.data.root_quat_w.clone()  # 桌子初始姿态
         
        self.left_link7_idx = self._robot.find_bodies("left_arm_link07")[0][0]    # 左手掌的索引
        self.right_link7_idx = self._robot.find_bodies("right_arm_link07")[0][0]   # 右手掌的索引

        self.right_hand_proximal_indices = [48, 34, 35, 37, 36]
        self.left_hand_proximal_indices = [43, 29, 30, 32, 31] # 左手手指指腹的索引

        left_link7_state  = self._robot.data.body_link_state_w[:,self.left_link7_idx,0:7]   # 左臂第7关节值
        right_link7_state  = self._robot.data.body_link_state_w[:,self.right_link7_idx,0:7]   # 左臂第7关节值

        self.origi_left_joint  = self._robot.data.joint_pos[:,self.left_arm_indices].clone()   # 关节变量
        self.origi_right_joint  = self._robot.data.joint_pos[:,self.right_arm_indices].clone()   # 关节变量

        self.pre_left_joint_p  = self._robot.data.joint_pos[:,self.left_arm_indices].clone()   # 上一个关节位置
        self.pre_right_joint_p  = self._robot.data.joint_pos[:,self.right_arm_indices].clone()   # 上一个关节位置

        self.pre_left_joint_v  = torch.zeros((self.num_envs,7),device=self.device)  # 上一个关节速度
        self.pre_right_joint_v  = torch.zeros((self.num_envs,7),device=self.device)  # 上一个关节速度

        self.detect_kettle_cup_move_t = torch.zeros((self.num_envs),device=self.device)

        self.target_decect_data = torch.empty([self.num_envs, 6], device=self.device)  # 
        self.image_data_revise = {}

        self.get_image_t = 0 


        # self.yolo_model = YOLO(r'/home/<USER>/ultralytics/runs/train/exp7/weights/best.pt').to(self.device)

        self.action_scale = self.cfg.action_scale    # 动作尺度
        self.robot_dof_lower_limits = self._robot.data.soft_joint_pos_limits[0, :, 0].to(device=self.device)  # 机器人关节限制最小值
        self.robot_dof_upper_limits = self._robot.data.soft_joint_pos_limits[0, :, 1].to(device=self.device)   # 机器人关节限制最大值

        # print('default_joint_pos',self._robot.data.default_joint_pos[0])
        self.robot_dof_targets = torch.zeros((self.num_envs, self._robot.num_joints), device=self.device)     # 机器人关节原始位置
        # 关节索引
        print(self._robot.find_joints('.*'))

        # 手握紧各关节值
        self.grasp_hand_upper = self.robot_dof_upper_limits[self.left_hand_indices]
        self.grasp_hand_lower = self.robot_dof_lower_limits[self.left_hand_indices]
        self.grasp_hand_base = (self.grasp_hand_upper - self.grasp_hand_lower)/100.0
        self.grasp_intent = torch.zeros((self.num_envs), device=self.device)
        print('robot_dof_lower_limits', self.robot_dof_lower_limits[self.left_hand_indices])
        print('robot_dof_upper_limits',self.robot_dof_upper_limits[self.left_hand_indices])

        # self.grasp_size = torch.zeros([self.num_envs,2], dtype=torch.float, device=self.device)

        self.flag = torch.tensor([0]*self.num_envs,device=self.device)

        self.threhold_curi = 0.05
        self.get_threhold_curi_target = torch.zeros(self.num_envs,device=self.device)
        # self.flag_1to2 = torch.tensor([False]*self.num_envs,device=self.device)
        # self.curricul_term = torch.tensor([False]*self.num_envs,device=self.device)
        self.actions_ago = torch.zeros([self.num_envs, self.cfg.action_space], device=self.device)



    # 建立场景
    def _setup_scene(self):
        self._robot = Articulation(self.cfg.Qiruirobot)
        self._kettle = RigidObject(self.cfg.Kettle)
        self._table = RigidObject(self.cfg.Table)
        self._cup = RigidObject(self.cfg.Cup)
        self._camera = TiledCamera(self.cfg.camera)
        # self._contact_forces_left_hand_intermediate = ContactSensor(self.cfg.contact_forces_left_hand_intermediate)
        # self._contact_forces_right_hand_intermediate= ContactSensor(self.cfg.contact_forces_right_hand_intermediate)
        self._contact_sensor_table = ContactSensor(self.cfg.contact_sensor_table)
        # self.scene.
        self.scene.articulations["robot"] = self._robot
        self.scene.rigid_objects["kettle"] = self._kettle
        self.scene.rigid_objects["table"] = self._table
        self.scene.rigid_objects["cup"] = self._cup
        self.scene.sensors["camera"] = self._camera
        # self.scene.sensors["contact_forces_left_hand_intermediate"] = self._contact_forces_left_hand_intermediate
        # self.scene.sensors["contact_forces_right_hand_intermediate"] = self._contact_forces_right_hand_intermediate
        self.scene.sensors["contact_sensor_table"] = self._contact_sensor_table

        # clone, filter, and replicate
        self.scene.clone_environments(copy_from_source=False)
        self.scene.filter_collisions(global_prim_paths=[])

        # add lights
        light_cfg = sim_utils.DomeLightCfg(intensity=2000.0, color=(0.75, 0.75, 0.75))
        light_cfg.func("/World/Light", light_cfg)

        cfg_ground = sim_utils.GroundPlaneCfg(size=(2000,2000))
        cfg_ground.func("/World/GroundPlane", cfg_ground)

    # pre-physics step calls
    # 手执行抓取和放开动作
    def _grasp(self, left_grasp_enable, right_grasp_enable):
        # #手张开
        # joint_lhand_loose = self._robot.data.soft_joint_pos_limits[:, self.left_hand_indices, 0]
        # self.robot_dof_targets[:, self.left_hand_indices] = joint_lhand_loose.clone()
        # joint_rhand_loose = self._robot.data.soft_joint_pos_limits[:, self.right_hand_indices, 0]
        # self.robot_dof_targets[:, self.right_hand_indices] = joint_rhand_loose.clone()
        self.robot_dof_targets[(~left_grasp_enable).nonzero(), self.left_hand_indices] = self.grasp_hand_lower
        self.robot_dof_targets[(~right_grasp_enable).nonzero(), self.right_hand_indices] = self.grasp_hand_lower

        #左手抓握
        self.robot_dof_targets[left_grasp_enable.nonzero(), self.left_hand_indices] = self.grasp_hand_upper * 0.35
        #右手抓握
        self.robot_dof_targets[right_grasp_enable.nonzero(), self.right_hand_indices] = self.grasp_hand_upper * 0.55

    # 动作前的函数
    def _pre_physics_step(self, actions: torch.Tensor):
        # 复位手的位置
        # left_grasp_enable = torch.zeros((self.num_envs), device=self.device, dtype=torch.bool)   
        # right_grasp_enable = torch.zeros((self.num_envs), device=self.device, dtype=torch.bool)
        # self._grasp(left_grasp_enable, right_grasp_enable)

        # term_left_hand = torch.flatten(self.c_l_d < 0.1)
        # term_right_hand = torch.flatten(self.k_r_d < 0.1)
        # self._grasp(term_left_hand, term_right_hand)  # 如果到达指定位置（距离小于0.1）则手抓紧、

        # 左臂右臂关节值赋值
        self.actions = actions.clone().clamp(-1.0, 1.0)
        # print('self.actions',self.actions)
        self.robot_dof_targets = self._robot.data.joint_pos.clone()
        # print('self.actions[:,-1]',self.actions[:,-1])
        # self._grasp(self.actions[:,-1])

        # self._grasp((self.flag==1) | (self.flag==2),(self.flag==1) | (self.flag==2))
        # self.robot_dof_targets[:, self.left_hand_indices] = self.robot_dof_targets[:,self.left_hand_indices] + self.actions[:,14:15] * self.grasp_hand_base
        # self.robot_dof_targets[:, self.right_hand_indices] = self.robot_dof_targets[:,self.right_hand_indices] + self.actions[:,15:16] * self.grasp_hand_base
        arm_indices = self.left_arm_indices[:5] + [self.left_arm_indices[6]] + self.right_arm_indices[:5] + [self.right_arm_indices[6]]
        self.robot_dof_targets[:, arm_indices] = self.robot_dof_targets[:,arm_indices] + self.actions[:,:12] * self.action_scale   # 给左臂和右臂赋值
        
        targets = self.robot_dof_targets
        self.robot_dof_targets[:] = torch.clamp(targets, self.robot_dof_lower_limits, self.robot_dof_upper_limits)
    
    # 执行动作
    def _apply_action(self):
        # 驱动关节
        self._robot.set_joint_position_target(self.robot_dof_targets)

    # post-physics step calls
    # 判断回合有没有结束
    def _get_dones(self) -> tuple[torch.Tensor, torch.Tensor]:
        print('self.flag',self.flag[:20])
        # print(self._robot.data.joint_pos[:,self.left_arm_indices]-self.robot_dof_targets[:,self.left_arm_indices])
#=========================================================计算第一阶段的终止条件==========================================================================
        # 水杯的位置判断
        cup_pos2 = self._cup.data.root_com_pos_w.clone()      # 水壶中心位置
        cup_pos2 = cup_pos2.unsqueeze(1).repeat(1,len(self.left_hand_proximal_indices),1)
        cup_pos2[:,0,2]+= 0.01   # 调整手指的目标距离
        cup_pos2[:,1,2]+= 0.01
        cup_pos2[:,3,2]-= 0.02
        cup_pos2[:,4,2]-= 0.03
        left_hand_proximal_state  = self._robot.data.body_link_state_w[:,self.left_hand_proximal_indices,0:7].clone()    # 左臂第7关节值
        self.c_l_d_cur = torch.mean(torch.norm(left_hand_proximal_state[:,:,:2] - cup_pos2[:,:,:2], p=2, dim=-1),dim=-1)
        self.c_l_d_cur = self.c_l_d_cur.unsqueeze(-1)
        self.z_dif_l_cur = torch.mean(torch.abs(left_hand_proximal_state[:,:,2] - cup_pos2[:,:,2]), dim=-1 )# 计算z轴的差值
        self.z_dif_l_cur = self.z_dif_l_cur.unsqueeze(-1)
        # self.c_l_d_cur = torch.cat((self.c_l_d_cur.unsqueeze(-1), z_dif_l.unsqueeze(-1)), dim=-1)
        left_link7_state  = self._robot.data.body_link_state_w[:,self.left_link7_idx,0:7].clone()   # 左臂第7关节值
        self.c_rot_cur = self.calculate_deflection_angle(left_link7_state[:,3:]) 
        
        
        kettle_pos2 = self._kettle.data.root_com_pos_w.clone()      # 水壶中心位置
        kettle_pos2 = kettle_pos2.unsqueeze(1).repeat(1,len(self.right_hand_proximal_indices),1)
        kettle_pos2[:,0,2]+= 0.02   # 调整手指的目标距离
        kettle_pos2[:,1,2]+= 0.01
        kettle_pos2[:,3,2]-= 0.03
        kettle_pos2[:,4,2]-= 0.05
        right_hand_proximal_state  = self._robot.data.body_link_state_w[:,self.right_hand_proximal_indices,0:7].clone()    # 左臂第7关节值
        self.k_r_d_cur = torch.mean(torch.norm(right_hand_proximal_state[:,:,:2] - kettle_pos2[:,:,:2], p=2, dim=-1),dim=-1)
        self.k_r_d_cur = self.k_r_d_cur.unsqueeze(1)
        self.z_dif_r_cur = torch.mean(torch.abs(right_hand_proximal_state[:,:,2] - kettle_pos2[:,:,2]), -1) # 计算z轴的差值
        self.z_dif_r_cur = self.z_dif_r_cur.unsqueeze(1)
        # self.k_r_d_cur = torch.cat((self.k_r_d_cur.unsqueeze(-1), z_dif_r.unsqueeze(-1)), dim=-1)
        right_link7_state  = self._robot.data.body_link_state_w[:,self.right_link7_idx,0:7].clone()   # 右臂第7关节值
        self.k_rot_cur = torch.pi - self.calculate_deflection_angle(right_link7_state[:,3:])

        # self.flag[torch.all(self.k_r_d_cur <= 0.08, dim=-1) & torch.all(self.c_l_d_cur <= 0.1, dim=-1)  & ((self.c_rot_cur < 0.2).squeeze(1)) & ((self.k_rot_cur < 0.2).squeeze(1))] = 1
        # self.flag[~(torch.all(self.k_r_d_cur <= 0.08, dim=-1) & torch.all(self.c_l_d_cur <= 0.1, dim=-1)  & ((self.c_rot_cur < 0.2).squeeze(1)) & ((self.k_rot_cur < 0.2).squeeze(1)))] = 0
        target_1 = (self.k_r_d_cur <= 0.08).squeeze(1)
        target_2 = (self.k_rot_cur < 0.2).squeeze(1)
        target_3 = (self.z_dif_r_cur < 0.02).squeeze(1)
        target_4 = (self.c_l_d_cur <= 0.1).squeeze(1)
        target_5 = (self.c_rot_cur < 0.2).squeeze(1)
        target_6 = (self.z_dif_l_cur < 0.02).squeeze(1)

        self.flag[target_1 & target_2 & target_3 & target_4 & target_5 & target_6] = 1
        self.flag[~(target_1 & target_2 & target_3 & target_4 & target_5 & target_6)] = 0
 

        #==========判断flag====================
        # 杯子、水壶以及桌子是否挪动
        self.detect_kettle_cup_move_t = self.detect_kettle_cup_move_t + 1
        kettle_root_pos = self._kettle.data.root_pos_w
        self.kettle_change_pos[self.detect_kettle_cup_move_t == 5] = kettle_root_pos[self.detect_kettle_cup_move_t == 5]
        self.k_change1 = torch.any(torch.abs(kettle_root_pos[:,:2] - self.kettle_change_pos[:,:2]) > 0.002, dim=1) & (torch.any(self.k_r_d_cur > 0.08, dim=-1)) & (self.detect_kettle_cup_move_t > 5)
        self.k_change2 = torch.any(torch.abs(kettle_root_pos[:,:2] - self.kettle_change_pos[:,:2]) > 0.03, dim=1) & (torch.all(self.k_r_d_cur <= 0.08, dim=-1)) & (self.detect_kettle_cup_move_t > 5)
        self.k_change = self.k_change1 | self.k_change2
        k_change = self.k_change.unsqueeze(1)

        cup_root_pos = self._cup.data.root_pos_w
        self.cup_change_pos[self.detect_kettle_cup_move_t == 5] = cup_root_pos[self.detect_kettle_cup_move_t == 5]
        self.c_change1 = torch.any(torch.abs(cup_root_pos[:,:2] - self.cup_change_pos[:,:2]) > 0.002, dim=1) & (torch.all(self.c_l_d_cur>0.1,dim=1)) & (self.detect_kettle_cup_move_t > 5)
        self.c_change2= torch.any(torch.abs(cup_root_pos[:,:2] - self.cup_change_pos[:,:2]) > 0.03, dim=1) & (torch.all(self.c_l_d_cur<=0.1,dim=1)) & (self.detect_kettle_cup_move_t > 5)
        self.c_change = self.c_change1 | self.c_change2
        c_change = self.c_change.unsqueeze(1)

        self.term_arm_hand = torch.any(torch.abs(self._contact_sensor_table.data.force_matrix_w) > 0.1,dim=(1,2,3))
        term_arm_hand = self.term_arm_hand.unsqueeze(1)
        # if torch.any(term_arm_hand):
        #     print('term_arm_hand  ',term_arm_hand)
        terminated_0 = torch.any(torch.concat((c_change,k_change,term_arm_hand), dim=1),dim=1)  # 
        
        #=========================================================计算第二阶段的终止条件==========================================================================
        # self.flag_1to2 = torch.tensor([False]*self.num_envs,device=self.device)  # 是否抓取成功
        # # 抓取成功否判断
        # threthod_force = 2
        # term_right_hand_grasp = torch.tensor([False]*self.num_envs,device=self.device)
        # if self._contact_forces_right_hand_intermediate.data.force_matrix_w != None:
        #     term_right_hand_grasp1 = torch.any(torch.abs(self._contact_forces_right_hand_intermediate.data.force_matrix_w) > threthod_force, dim=-1).squeeze(1)
        #     # print('term_right_hand_grasp1.shape',term_right_hand_grasp1.shape)
        #     term_right_hand_grasp = torch.sum(term_right_hand_grasp1,dim=1) >= 3
        #     print('contact_Force',self._contact_forces_right_hand_intermediate.data.force_matrix_w[:2])
        # self.flag_1to2[term_right_hand_grasp & (self.flag == 1) & (torch.all(self.k_r_d_cur<0.08,dim=1))] = True
        # self.flag[term_right_hand_grasp & (self.flag == 1) & (torch.all(self.k_r_d_cur<0.08,dim=1))] = 2
        
        terminated = terminated_0 | ((self._kettle.data.root_com_pos_w[:,2] < (self.kettle_z-0.01)) & (self.detect_kettle_cup_move_t > 5)) #terminated_2
        # print("terminated",terminated)
        # self.Hand_end = (self.grasp_intent >= 0.8)
        # terminated = terminated | self.Hand_end
        # terminated = terminated | (self.flag == 2)
        truncated = self.episode_length_buf >= self.max_episode_length - 1

        self.end = terminated | truncated

        # print("truncated",truncated)
        # if torch.any(terminated):
        #     print('terminated',terminated)
        #     print('k_change',k_change)
        #     print('term_arm_hand',term_arm_hand)
        return terminated, truncated

    def _get_rewards(self) -> torch.Tensor:
        # 将指标归一化到[0, 1]之间
        self.max_d = 2.0
        self.max_z = 1.0
        self.max_rot = torch.pi

        norm_c_l_d_cur = torch.clamp(self.c_l_d_cur / self.max_d, 0, 1)
        norm_z_diff_l_cur = torch.clamp(self.z_dif_l_cur / self.max_z, 0, 1)
        norm_c_rot_cur = torch.clamp(self.c_rot_cur / self.max_rot, 0, 1)

        norm_c_l_d_last = torch.clamp(self.region_c_l_d / self.max_d, 0, 1)
        norm_z_dif_l_last = torch.clamp(self.region_z_dif_l / self.max_z, 0, 1)
        norm_c_rot_last = torch.clamp(self.region_c_rot / self.max_rot, 0, 1)


        norm_k_r_d_cur = torch.clamp(self.k_r_d_cur / self.max_d, 0, 1)
        norm_z_dif_r_cur = torch.clamp(self.z_dif_r_cur / self.max_z, 0, 1)
        norm_k_rot_cur = torch.clamp(self.k_rot_cur / self.max_rot, 0, 1)

        norm_k_r_d_last = torch.clamp(self.region_k_r_d / self.max_d, 0, 1)
        norm_z_dif_r_last = torch.clamp(self.region_z_dif_r / self.max_z, 0, 1)
        norm_k_rot_last = torch.clamp(self.region_k_rot / self.max_rot, 0, 1)

        print("k_r_d ",self.k_r_d_cur[:4])
        print("z_diff_r ",self.z_dif_r_cur[:4])
        print("k_rot ",self.k_rot_cur[:4])
        print("c_l_d ",self.c_l_d_cur[:4])
        print("z_diff_l ",self.z_dif_l_cur[:4])
        print("c_rot ",self.c_rot_cur[:4])
        # ------------------------------ 支配关系 --------------------------------------------------
        # 定义一个函数判断当前指标是否支配了前一个指标
        def dominates(new_region, prev_region):
            return torch.all(new_region <= prev_region, dim = 1)
            # return torch.all((new_region - prev_region)<0.01, dim = 1) & torch.any(new_region < prev_region, dim = 1)
        # 定义一个函数判断当前指标是否被支配了前一个指标
        def dominated(new_region, prev_region):
            return torch.all(new_region > prev_region, dim = 1)
            # return torch.all((new_region - prev_region)<0.01, dim = 1) & torch.any(new_region < prev_region, dim = 1)

        # ------------------------------ 联合奖励 --------------------------------------------------
        # 初始化奖励
        rewards = torch.zeros(self.num_envs, dtype=torch.float, device=self.device)

        # 只有新的指标支配了上一个指标时，才给奖励
        cur_index = torch.cat((norm_c_l_d_cur,norm_z_diff_l_cur,norm_c_rot_cur, norm_k_r_d_cur, norm_z_dif_r_cur, norm_k_rot_cur),dim=1)
        last_index = torch.cat((norm_c_l_d_last,norm_z_dif_l_last, norm_c_rot_last, norm_k_r_d_last, norm_z_dif_r_last, norm_k_rot_last),dim=1)
        condition_d = dominates(cur_index, last_index) # & (self.actions[:,-1]>-1)
        condition_d_V = torch.norm(last_index,dim=1,p=2) - torch.norm(cur_index,dim=1,p=2)
        condition_ed = dominated(cur_index, last_index)
        condition_not_d = ~(condition_d | condition_ed)
        condition_t = (torch.norm(cur_index,dim=1,p=2) <= 0.05) # | (self.flag == 2)

        print('condition_d_V ',condition_d_V[condition_d])
        rewards[condition_d & (self.flag == 0) & (condition_d_V>=0.001)] = 1.0
        rewards[condition_ed & (self.flag == 0)] = -1.5
        rewards[condition_not_d & (self.flag == 0)]  = -0.8

        # print('     ')
        print('     ')

        # ------------------------------ 最终奖励 --------------------------------------------------
        # rewards += 0.5 * (
        #     k_change_penalty + c_change_penalty + term_arm_hand_penalty
        # )
        print('self.threhold_curi',self.threhold_curi)
        rewards[self.term_arm_hand] += -10.0
        rewards[self.k_change] += -5.0
        # rewards[self.Hand_end] += -5.0
        # 
        rewards[self.flag == 1] = 20.0
        # rewards[self.flag_1to2] = 6.0
        # rewards[self.flag == 2] += 10.0

        return rewards

    # 复原函数
    def _reset_idx(self, env_ids: torch.Tensor | None):
        if env_ids is None:
            env_ids = self._robot._ALL_INDICES
        super()._reset_idx(env_ids)
        # 机器人状态重置
        robot_state = self._robot.data.default_root_state.clone()
        robot_id_state = robot_state[env_ids, :].clone()
        robot_id_state[:, 2]  = robot_id_state[:, 2] + sample_uniform(
            -self.threhold_curi+0.03,
            self.threhold_curi-0.03,
            len(env_ids),
            self.device,
        )
        robot_id_state[:, :3] += self.scene.env_origins[env_ids, :]
        self._robot.write_root_link_pose_to_sim(robot_id_state[:, :7],env_ids=env_ids)
        self._robot.write_root_com_velocity_to_sim(robot_id_state[:, 7:],env_ids=env_ids)
        joint_pos = self._robot.data.default_joint_pos[env_ids]
        joint_pos = torch.clamp(joint_pos, self.robot_dof_lower_limits, self.robot_dof_upper_limits)
        joint_vel = torch.zeros_like(joint_pos)
        self._robot.set_joint_position_target(joint_pos, env_ids=env_ids)
        self._robot.write_joint_state_to_sim(joint_pos, joint_vel, env_ids=env_ids)

        # 杯子和饮料瓶状态重置
        kettle_orinal_pos = self.kettle_orinal_pos[env_ids, :].clone() 
        cup_orinal_pos = self.cup_orinal_pos[env_ids, :].clone()
        kettle_orinal_pos[:, 0]  = kettle_orinal_pos[:, 0] + sample_uniform(
            -self.threhold_curi,
            self.threhold_curi,
            len(env_ids),
            self.device,
        )
        kettle_orinal_pos[:, 1] = kettle_orinal_pos[:, 1] + sample_uniform(
            -self.threhold_curi,
            self.threhold_curi,
            len(env_ids),
            self.device,
        )

        cup_orinal_pos[:, 0] = cup_orinal_pos[:, 0] + sample_uniform(
            -self.threhold_curi,
            self.threhold_curi,
            len(env_ids),
            self.device,
        )
        cup_orinal_pos[:, 1] = cup_orinal_pos[:, 1] + sample_uniform(
            -self.threhold_curi,
            self.threhold_curi,
            len(env_ids),
            self.device,
        )
        
        self._kettle.write_root_pose_to_sim(torch.cat((kettle_orinal_pos,self.kettle_orinal_quat[env_ids,:]),dim=-1), env_ids)
        self._cup.write_root_pose_to_sim(torch.cat((cup_orinal_pos,self.cup_orinal_quat[env_ids,:]),dim=-1), env_ids)
        self._table.write_root_pose_to_sim(torch.cat((self.table_orinal_pos[env_ids,:],self.table_orinal_quat[env_ids,:]),dim=-1), env_ids)
        self._kettle.write_root_velocity_to_sim(torch.zeros((len(env_ids), 6), device=self.device), env_ids)
        self._cup.write_root_velocity_to_sim(torch.zeros((len(env_ids), 6), device=self.device), env_ids)
        self._table.write_root_velocity_to_sim(torch.zeros((len(env_ids), 6), device=self.device), env_ids)
        # 关节位置和速度变量重置
        pre_left_joint_p= self._robot.data.joint_pos[:,self.left_arm_indices].clone()   # 上一个关节位置
        pre_right_joint_p = self._robot.data.joint_pos[:,self.right_arm_indices].clone()   # 上一个关节位置
        self.pre_left_joint_p[env_ids] = pre_left_joint_p[env_ids,:].clone()   # 上一个关节位置
        self.pre_right_joint_p[env_ids] = pre_right_joint_p[env_ids,:].clone()   # 上一个关节位置置
        self.pre_left_joint_v[env_ids]  = torch.zeros((len(env_ids), 7), device=self.device)  # 上一个关节速度
        self.pre_right_joint_v[env_ids]  = torch.zeros((len(env_ids), 7), device=self.device) # 上一个关节速度

        self.detect_kettle_cup_move_t[env_ids] = 0
        self.image_data_revise.update({k.item():3 for k in env_ids})
        self.grasp_intent[env_ids] = 0
        self.flag[env_ids] = 0

    # 获取观测值函数
    def _get_observations(self) -> dict:
        # 水壶的位置距离判断
        kettle_pos2 = self._kettle.data.root_com_pos_w.clone()      # 水壶中心位置
        kettle_pos2 = kettle_pos2.unsqueeze(1).repeat(1,len(self.right_hand_proximal_indices),1)
        kettle_pos2[:,0,2]+= 0.02   # 调整手指的目标距离
        kettle_pos2[:,1,2]+= 0.01
        kettle_pos2[:,3,2]-= 0.03
        kettle_pos2[:,4,2]-= 0.05
        # print('kettle_pos2  ',kettle_pos2)
        right_hand_proximal_state  = self._robot.data.body_link_state_w[:,self.right_hand_proximal_indices,0:7].clone()    # 
        right_link7_state  = self._robot.data.body_link_state_w[:,self.right_link7_idx,0:7].clone()   # 右臂第7关节值
        # print('right_hand_proximal_state[:,:,:3] ',right_hand_proximal_state[:,:,:3])
        self.region_k_r_d = torch.mean(torch.norm(right_hand_proximal_state[:,:,:2] - kettle_pos2[:,:,:2], p=2, dim=-1),dim=-1)
        self.region_k_r_d = self.region_k_r_d.unsqueeze(-1)
        self.region_z_dif_r = torch.mean(torch.abs(right_hand_proximal_state[:,:,2] - kettle_pos2[:,:,2]), -1) # 计算z轴的差值
        self.region_z_dif_r = self.region_z_dif_r.unsqueeze(-1)
        # self.region_k_r_d = torch.cat((self.region_k_r_d.unsqueeze(-1), z_dif_r.unsqueeze(-1)), dim=-1)
        self.region_k_rot = torch.pi - self.calculate_deflection_angle(right_link7_state[:,3:])

        # 水杯的位置判断
        cup_pos2 = self._cup.data.root_com_pos_w.clone()      # 水壶中心位置
        cup_pos2 = cup_pos2.unsqueeze(1).repeat(1,len(self.left_hand_proximal_indices),1)
        cup_pos2[:,0,2]+= 0.01   # 调整手指的目标距离
        cup_pos2[:,1,2]+= 0.01
        cup_pos2[:,3,2]-= 0.02
        cup_pos2[:,4,2]-= 0.03
        left_hand_proximal_state  = self._robot.data.body_link_state_w[:,self.left_hand_proximal_indices,0:7].clone()    # 左臂第7关节值
        self.region_c_l_d = torch.mean(torch.norm(left_hand_proximal_state[:,:,:2] - cup_pos2[:,:,:2], p=2, dim=-1),dim=-1)
        self.region_c_l_d = self.region_c_l_d.unsqueeze(-1)
        self.region_z_dif_l = torch.mean(torch.abs(left_hand_proximal_state[:,:,2] - cup_pos2[:,:,2]), dim=-1 )# 计算z轴的差值
        self.region_z_dif_l = self.region_z_dif_l.unsqueeze(-1)
        # self.region_c_l_d = torch.cat((self.region_c_l_d.unsqueeze(-1), z_dif_l.unsqueeze(-1)), dim=-1)
        left_link7_state  = self._robot.data.body_link_state_w[:,self.left_link7_idx,0:7].clone()   # 左臂第7关节值
        self.region_c_rot = self.calculate_deflection_angle(left_link7_state[:,3:]) 


        camera_data_depth = self._camera.data.output["depth"]
        camera_data_depth = camera_data_depth.permute(0, 3, 1, 2)
        # print(camera_data_depth.shape)
        # print('camera_data_depth',camera_data_depth.max())
        # max_tensor = torch.max(camera_data_depth,dim=(1,2))
        # camera_data_depth = camera_data_depth + torch.randn_like(camera_data_depth)*0.01

        # 加噪声
        # prob = 0.1
        # mask = torch.randn_like(camera_data_depth) < prob
        # camera_data_depth[mask] = 0.  # 椒噪声
        # noisy_image = torch.rand_like(noisy_image) * 0.05 + noisy_image
        # noisy_image = torch.poisson(noisy_image * 20) / 20

        # camera_data_depth[camera_data_depth > float(1.0)] = 0.0
        # # camera_data_depth = camera_data_depth
        # resize_transform = transforms.Resize((224, 224)) 
        # camera_data_depth = resize_transform(camera_data_depth)
        # print(camera_data_depth.shape)
        Zeros_index = (camera_data_depth > float(1.0))
        camera_data_depth[Zeros_index] = 0.0

        


        kettle_pos = self._kettle.data.root_com_pos_w.clone()      # 水壶中心位置
        kettle_pos[:,1] = kettle_pos[:,1] + 0.08
        kettle_pos[:,2] = kettle_pos[:,2] + 0.03

        cup_pos = self._cup.data.root_com_pos_w.clone()      # 水壶中心位置
        cup_pos[:,1] = cup_pos[:,1] + 0.1
        cup_pos[:,2] = cup_pos[:,2] + 0.03

        keys_to_remove = [k for k, v in self.image_data_revise.items() if v == 0]
        # 删除这些键
        for k in keys_to_remove:
            del self.image_data_revise[k]
        # print('self.image_data_revise',self.image_data_revise)
        self.target_decect_data[list(self.image_data_revise.keys()),:] = torch.cat((cup_pos[list(self.image_data_revise.keys()),:], kettle_pos[list(self.image_data_revise.keys()),:]), dim=-1)

        for key in self.image_data_revise:
            self.image_data_revise[key] -= 1

        
        End_pos_quat = torch.zeros((self.num_envs, 14), device=self.device)
        Object_poses = torch.zeros((self.num_envs, 6), device=self.device)
        Object_quat = torch.zeros((self.num_envs, 8), device=self.device)
        def quaternion_inverse(q):
            """计算四元数的逆（假设四元数是单位四元数）"""
            q_conj = np.array([q[0], -q[1], -q[2], -q[3]])  # 共轭
            return q_conj / np.dot(q, q)  # 归一化（如果不是单位四元数）

        def quaternion_multiply(q1, q2):
            """计算两个四元数的乘积"""
            w1, x1, y1, z1 = q1
            w2, x2, y2, z2 = q2
            return np.array([
                w1*w2 - x1*x2 - y1*y2 - z1*z2,
                w1*x2 + x1*w2 + y1*z2 - z1*y2,
                w1*y2 - x1*z2 + y1*w2 + z1*x2,
                w1*z2 + x1*y2 - y1*x2 + z1*w2
            ])

        def rotate_vector_by_quaternion(v, q):
            """使用四元数旋转三维向量"""
            q_vec = np.hstack(([0], v))  # 扩展为四元数形式
            q_rotated = quaternion_multiply(quaternion_multiply(q, q_vec), quaternion_inverse(q))
            return q_rotated[1:]  # 提取旋转后的向量

        def transform_pose(q_A_B, p_A_B, q_C_B, p_C_B):
            """已知A在B的位姿，C在B的位姿，计算A在C坐标系下的四元数和笛卡尔坐标"""
            q_C_B_inv = quaternion_inverse(q_C_B)
            q_A_C = quaternion_multiply(q_C_B_inv, q_A_B)
            
            p_rel = p_A_B - p_C_B  # A相对C的平移
            p_A_C = rotate_vector_by_quaternion(p_rel, q_C_B_inv)  # 变换到C坐标系
            
            return q_A_C, p_A_C
        
        def transform_p(p_A_B, q_C_B, p_C_B):
            """已知A在B的位置，C在B的位姿，计算A在C坐标系下的笛卡尔坐标"""
            q_C_B_inv = quaternion_inverse(q_C_B)
            
            p_rel = p_A_B - p_C_B  # A相对C的平移
            p_A_C = rotate_vector_by_quaternion(p_rel, q_C_B_inv)  # 变换到C坐标系
            
            return p_A_C
        
        def transform_q(q_A_B, q_C_B):
            """已知A在B的位置，C在B的位姿，计算A在C坐标系下的笛卡尔坐标"""
            q_C_B_inv = quaternion_inverse(q_C_B)
            q_A_C = quaternion_multiply(q_C_B_inv, q_A_B)
            
            return q_A_C
        
        # 示例四元数和位移向量（w, x, y, z 格式）
        for i in range(self.num_envs):
            q_A_B1 = left_link7_state[i,3:].cpu().numpy() # 左臂A相对B
            p_A_B1 = left_link7_state[i,:3].cpu().numpy()  # A在B下的坐标

            q_A_B2 = right_link7_state[i,3:].cpu().numpy() # 右臂A相对B
            p_A_B2 = right_link7_state[i,:3].cpu().numpy()  # A在B下的坐标

            q_C_B = self._robot.data.root_quat_w[i,:].clone().cpu().numpy()  # C相对B
            p_C_B = self._robot.data.root_pos_w[i,:].clone().cpu().numpy()  # C在B下的坐标
            # print('p_C_B',p_C_B)

            q_A_C1, p_A_C1 = transform_pose(q_A_B1, p_A_B1, q_C_B, p_C_B)
            q_A_C2, p_A_C2 = transform_pose(q_A_B2, p_A_B2, q_C_B, p_C_B)

            End_pos_quat[i,:7] = torch.tensor(np.hstack((p_A_C1, q_A_C1)),device=self.device)    # 左臂末端相对机器人的位姿
            End_pos_quat[i,7:] = torch.tensor(np.hstack((p_A_C2, q_A_C2)),device=self.device)    # 右臂末端相对机器人的位姿

            Object_poses[i,:3] = torch.tensor(transform_p(self.target_decect_data[i,:3].cpu().numpy(), q_C_B, p_C_B),device=self.device) 
            Object_poses[i,3:] = torch.tensor(transform_p(self.target_decect_data[i,3:].cpu().numpy(), q_C_B, p_C_B),device=self.device) 

            Object_quat[i,:4] = torch.tensor(transform_q([0.5, 0.5, -0.5, -0.5], q_C_B),device=self.device)
            Object_quat[i,4:] = torch.tensor(transform_q([0.5, -0.5, 0.5, -0.5], q_C_B),device=self.device)
        # print('Object_poses',Object_poses[:2])
        # print(transform_quaternion(left_link7_state[0,3:].cpu().numpy(),self._robot.data.root_quat_w[0,:].cpu().numpy()))

        cur_left_joint_p  = self._robot.data.joint_pos[:,self.left_arm_indices].clone()   # 上一个关节位置
        cur_right_joint_p  = self._robot.data.joint_pos[:,self.right_arm_indices].clone()   # 上一个关节位置

        # print('真实角度差 ',self.robot_dof_targets[:,self.left_arm_indices + self.right_arm_indices] - self._robot.data.joint_pos[:,self.left_arm_indices+ self.right_arm_indices])

        cur_left_joint_v = (cur_left_joint_p - self.pre_left_joint_p) # /self.physics_dt/self.cfg.decimation
        cur_right_joint_v = (cur_right_joint_p - self.pre_right_joint_p)  # /self.physics_dt/self.cfg.decimation

        self.pre_left_joint_p  = self._robot.data.joint_pos[:,self.left_arm_indices].clone()   # 上一个关节位置
        self.pre_right_joint_p  = self._robot.data.joint_pos[:,self.right_arm_indices].clone()   # 上一个关节位置

        # self.pre_left_joint_v  = cur_left_joint_v.clone()  # 上一个关节速度
        # self.pre_right_joint_v  = cur_right_joint_v.clone() # 上一个关节速度

        # dof_var = self._robot.data.joint_pos[:,self.left_arm_indices + self.right_arm_indices]   # 关节变量
        # print(dof_pos.shape)
        flag = self.flag.clone()
        
        # 归一化处理
        def normalize(x, x_min, x_max):
            return 2 * (x - x_min) / (x_max - x_min) - 1  # 映射到 [-1, 1]
        
        object_posMax = torch.tensor([0.6, 0.25, 0.4, 0.6, 0.25, 0.4],device=self.device)
        object_posMin = torch.tensor([0.2, -0.25, 0.1, 0.2, -0.25, 0.1],device=self.device)
        Object_poses_Norm = normalize(Object_poses, object_posMin, object_posMax)
        # print('Object_poses',Object_poses)
        # Object_poses = Object_poses * 10

        dof_var = torch.concat((cur_left_joint_p, cur_left_joint_v, End_pos_quat[:,:7], Object_poses_Norm[:,:3],Object_poses[:,:3],
                                cur_right_joint_p, cur_right_joint_v,End_pos_quat[:,7:], Object_poses_Norm[:,3:],Object_poses[:,3:]),dim=1) 
        # print('cur_left_joint_p',cur_left_joint_p)
        # print('cur_right_joint_p',cur_right_joint_p)
        # print('cur_left_joint_v',cur_left_joint_v)
        # print('cur_right_joint_v',cur_right_joint_v)
        # print('left_link7_state',left_link7_state)
        # print('right_link7_state',right_link7_state)
        obs = dof_var

        # print('obs.shape',obs.shape)

        observations = {"policy": obs.clone()}

        # if self.cfg.write_image_to_file:
        #     save_images_to_file(observations["policy"], f"cartpole_{data_type}.png")

        return observations

    def quaternion_to_rotation_matrix(self,q):
        """
        将四元数转换为旋转矩阵
        :param q: 四元数张量，形状为 (N, 4)
        :return: 旋转矩阵张量，形状为 (N, 3, 3)
        """
        N = q.shape[0]
        w = q[:, 0]
        x = q[:, 1]
        y = q[:, 2]
        z = q[:, 3]

        R = torch.stack([
            1 - 2 * y**2 - 2 * z**2, 2 * x * y - 2 * z * w, 2 * x * z + 2 * y * w,
            2 * x * y + 2 * z * w, 1 - 2 * x**2 - 2 * z**2, 2 * y * z - 2 * x * w,
            2 * x * z - 2 * y * w, 2 * y * z + 2 * x * w, 1 - 2 * x**2 - 2 * y**2
        ], dim=1).view(N, 3, 3)
        return R


    def calculate_deflection_angle(self,q):
        """
        计算坐标系 A 的 y 轴与世界坐标系 B 的 z 轴的偏转角
        :param q: 坐标系 A 相对于坐标系 B 的四元数张量，形状为 (N, 4)
        :return: 偏转角张量，形状为 (N, 1)
        """
        # 将四元数转换为旋转矩阵
        R = self.quaternion_to_rotation_matrix(q)

        # 坐标系 A 的 y 轴在世界坐标系中的表示
        y_axis_A_in_B = R[:, :, 1]

        # 世界坐标系的 z 轴
        z_axis_B = torch.tensor([0, 0, 1], dtype=torch.float32,device = self.device).unsqueeze(0).expand(q.shape[0], -1)

        # 计算两个向量的点积
        dot_product = torch.sum(y_axis_A_in_B * z_axis_B, dim=1)

        # 计算两个向量的模长
        norm_y_axis_A_in_B = torch.norm(y_axis_A_in_B, dim=1)
        norm_z_axis_B = torch.norm(z_axis_B, dim=1)

        # 计算夹角的余弦值
        cos_angle = dot_product / (norm_y_axis_A_in_B * norm_z_axis_B)

        # 计算夹角（弧度）
        angle = torch.acos(cos_angle).unsqueeze(1)

        return angle



