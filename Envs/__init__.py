# Copyright (c) 2022-2025, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
Humanoid locomotion environment.
"""

import gymnasium as gym
import agents

##
# Register Gym environments.
##

gym.register(
    id="Isaac-QituiItem-Direct-v0",
    entry_point=f"{__name__}.Qirui_env2:QiruiEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.Qirui_env2:QiruiEnvCfg",
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_QiruiItem_ppo_cfg.yaml",
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_QiruiItem_cfg:QiruiItemPPORunnerCfg",
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",
    },
)

# gym.register(
#     id="Isaac-QituiItem-Direct-v1",
#     entry_point=f"{__name__}.Qirui_env2_1:QiruiEnv",
#     disable_env_checker=True,
#     kwargs={
#         "env_cfg_entry_point": f"{__name__}.Qirui_env2_1:QiruiEnvCfg",
#         "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_QiruiItem_ppo_cfg.yaml",
#         "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_QiruiItem_cfg:QiruiItemPPORunnerCfg",
#         "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",
#     },
# )

gym.register(
    id="Isaac-QituiItem-Direct-v3_joint",
    entry_point=f"{__name__}.Qirui_env3:QiruiEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.Qirui_env3:QiruiEnvCfg",
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_QiruiItem_ppo_cfg.yaml",
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_QiruiItem_cfg:QiruiItemPPORunnerCfg",
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",
    },
)

gym.register(
    id="Isaac-QituiItem-Direct-v3_pos",
    entry_point=f"{__name__}.Qirui_env3_2:QiruiEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.Qirui_env3_2:QiruiEnvCfg",
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_QiruiItem_ppo_cfg.yaml",
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_QiruiItem_cfg:QiruiItemPPORunnerCfg",
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",
    },
)

gym.register(
    id="Isaac-QituiItem-Direct-v3_pos_yolo",
    entry_point=f"{__name__}.Qirui_env3_yolo:QiruiEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.Qirui_env3_yolo:QiruiEnvCfg",
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_QiruiItem_ppo_yolo_cfg.yaml",
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_yolo_QiruiItem_cfg:QiruiItemPPORunnerCfg",
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",
    },
)

gym.register(
    id="Isaac-QituiItem-Direct-v3_pos_yolo_r",
    entry_point=f"{__name__}.Qirui_env3_yolo:QiruiEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.Qirui_env3_yolo:QiruiEnvCfg",
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_QiruiItem_ppo_yolo_cfg.yaml",
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_QiruiItem_yolo_recurrent_cfg:QiruiItemPPORunnerCfg",
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",
    },
)

gym.register(
    id="Isaac-QituiItem-Direct-v3_pos_recurrent",
    entry_point=f"{__name__}.Qirui_env3_flag_s:QiruiEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.Qirui_env3_flag_s:QiruiEnvCfg",
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_QiruiItem_ppo_cfg.yaml",
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_QiruiItem_recurrent_cfg:QiruiItemPPORunnerCfg",
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",
    },
)

gym.register(
    id="Isaac-QituiItem-Direct-v3_pos_flag",
    entry_point=f"{__name__}.Qirui_env3_flag_s:QiruiEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.Qirui_env3_flag_s:QiruiEnvCfg",
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_QiruiItem_ppo_cfg.yaml",
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_QiruiItem_cfg:QiruiItemPPORunnerCfg",
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",
    },
)

gym.register(
    id="Isaac-QituiItem-Direct-v3_pos_flag2_PB2",
    entry_point=f"{__name__}.Qirui_env3_flag_s2_PB2:QiruiEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.Qirui_env3_flag_s2_PB2:QiruiEnvCfg",
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_QiruiItem_ppo_cfg.yaml",
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_QiruiItem_cfg2:QiruiItemPPORunnerCfg",
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",
    },
)

gym.register(
    id="Isaac-QituiItem-Direct-v3_pos_flag2_point",
    entry_point=f"{__name__}.Qirui_env3_flag_s2_points:QiruiEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.Qirui_env3_flag_s2_points:QiruiEnvCfg",
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_QiruiItem_ppo_cfg.yaml",
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_QiruiItem_cfg2_point:QiruiItemPPORunnerCfg",
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",
    },
)

gym.register(
    id="Isaac-QituiItem-Direct-v3_pos_flag2_point_v2",
    entry_point=f"{__name__}.Qirui_env3_flag_s2_points_v2:QiruiEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.Qirui_env3_flag_s2_points_v2:QiruiEnvCfg",
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_QiruiItem_ppo_cfg.yaml",
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_QiruiItem_cfg2_point:QiruiItemPPORunnerCfg",
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",
    },
)

gym.register(
    id="Isaac-QituiItem-Direct-v3_pos_flag2_point_v3",
    entry_point=f"{__name__}.Qirui_env3_flag_s2_points_v3:QiruiEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.Qirui_env3_flag_s2_points_v3:QiruiEnvCfg",
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_QiruiItem_ppo_cfg.yaml",
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_QiruiItem_cfg2_point_v3:QiruiItemPPORunnerCfg",
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",
    },
)

gym.register(
    id="Isaac-QituiItem-Direct-v3_pos_flag2_v1",
    entry_point=f"{__name__}.Qirui_env3_flag_s2_v1:QiruiEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.Qirui_env3_flag_s2_v1:QiruiEnvCfg",
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_QiruiItem_ppo_cfg.yaml",
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_QiruiItem_cfg2_v1:QiruiItemPPORunnerCfg",
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",
    },
)

gym.register(
    id="Isaac-QituiItem-Direct-v3_pos_flagD",
    entry_point=f"{__name__}.Qirui_env3_flag_s_D:QiruiEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.Qirui_env3_flag_s_D:QiruiEnvCfg",
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_QiruiItem_ppo_cfg.yaml",
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_QiruiItem_cfgDepth:QiruiItemPPORunnerCfg",
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",
    },
)

gym.register(
    id="Isaac-QituiItem-Direct-v3_pos_PB",
    entry_point=f"{__name__}.Qirui_env3_flag_s_PB:QiruiEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.Qirui_env3_flag_s_PB:QiruiEnvCfg",
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_QiruiItem_ppo_cfg.yaml",
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_QiruiItem_cfgPB:QiruiItemPPORunnerCfg",
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",
    },
)

gym.register(
    id="Isaac-QituiItem-Direct-v3_pos_PB2",
    entry_point=f"{__name__}.Qirui_env3_flag_s_PB2:QiruiEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.Qirui_env3_flag_s_PB2:QiruiEnvCfg",
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_QiruiItem_ppo_cfg.yaml",
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_QiruiItem_cfgPB:QiruiItemPPORunnerCfg",
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",
    },
)

gym.register(
    id="Isaac-QituiItem-Direct-v3_pos_PB_RA",
    entry_point=f"{__name__}.Qirui_env3_flag_s_PB_RightArm:QiruiEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.Qirui_env3_flag_s_PB_RightArm:QiruiEnvCfg",
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_QiruiItem_ppo_cfg.yaml",
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_QiruiItem_cfgPB_R:QiruiItemPPORunnerCfg",
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",
    },
)

gym.register(
    id="Isaac-QituiItem-Direct-v3_pos_PB_RA2",
    entry_point=f"{__name__}.Qirui_env3_flag_s_PB_RightArm_2:QiruiEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.Qirui_env3_flag_s_PB_RightArm_2:QiruiEnvCfg",
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_QiruiItem_ppo_cfg.yaml",
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_QiruiItem_cfgPB_R:QiruiItemPPORunnerCfg",
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",
    },
)

gym.register(
    id="Isaac-QituiItem-Direct-v3_pos_RA",
    entry_point=f"{__name__}.Qirui_env3_flag_s_RightArm:QiruiEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.Qirui_env3_flag_s_RightArm:QiruiEnvCfg",
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_QiruiItem_ppo_cfg.yaml",
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_QiruiItem_cfg_R:QiruiItemPPORunnerCfg",
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",
    },
)

gym.register(
    id="Isaac-QituiItem-Direct-v3_pos_RA2",
    entry_point=f"{__name__}.Qirui_env3_flag_s_RightArm_2:QiruiEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.Qirui_env3_flag_s_RightArm_2:QiruiEnvCfg",
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_QiruiItem_ppo_cfg.yaml",
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_QiruiItem_cfg_R:QiruiItemPPORunnerCfg",
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",
    },
)

gym.register(
    id="Isaac-QituiItem-Direct-v3_pos_LA2",
    entry_point=f"{__name__}.Qirui_env3_flag_s_LeftArm_2:QiruiEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.Qirui_env3_flag_s_LeftArm_2:QiruiEnvCfg",
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_QiruiItem_ppo_cfg.yaml",
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_QiruiItem_cfg_L:QiruiItemPPORunnerCfg",
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",
    },
)

gym.register(
    id="Isaac-QiJiang-v1",
    entry_point=f"{__name__}.QiJiang_env:QiJiangEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.QiJiang_env:QiJiangEnvCfg",
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_QiruiItem_ppo_cfg.yaml",
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_QiJiang_cfg:QiJiangItemPPORunnerCfg",
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",
    },
)

gym.register(
    id="Isaac-QituiItem-Direct-v3_pos_RA2_teacher",
    entry_point=f"{__name__}.Qirui_env3_flag_s_RightArm_2_teacher:QiruiEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.Qirui_env3_flag_s_RightArm_2_teacher:QiruiEnvCfg",
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_QiruiItem_ppo_cfg.yaml",
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_QiruiItem_cfg_R_teacher:QiruiItemPPORunnerCfg",
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",
    },
)

gym.register(
    id="Isaac-QituiItem-Direct-v3_pos_PB_LA",
    entry_point=f"{__name__}.Qirui_env3_flag_s_PB_LeftArm:QiruiEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.Qirui_env3_flag_s_PB_LeftArm:QiruiEnvCfg",
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_QiruiItem_ppo_cfg.yaml",
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_QiruiItem_cfgPB_L:QiruiItemPPORunnerCfg",
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",
    },
)

gym.register(
    id="Isaac-QituiItem-Direct-play",
    entry_point=f"{__name__}.Qirui_env2_play:QiruiEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.Qirui_env2_play:QiruiEnvCfg",
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_QiruiItem_ppo_cfg.yaml",
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_QiruiItem_cfg:QiruiItemPPORunnerCfg",
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",
    },
)

gym.register(
    id="Isaac-Cartpole-Direct-v7",
    entry_point=f"{__name__}.cartpole_env:CartpoleEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.cartpole_env:CartpoleEnvCfg",
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_ppo_cfg.yaml",
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_cfg:CartpolePPORunnerCfg",
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",
        "sb3_cfg_entry_point": f"{agents.__name__}:sb3_ppo_cfg.yaml",
    },
)
