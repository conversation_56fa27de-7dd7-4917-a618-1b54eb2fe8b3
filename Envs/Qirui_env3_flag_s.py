
# Copyright (c) 2022-2025, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause
#==============================================================================未使用逆运动学知识=============================================================================================
#==============================================================================考虑水壶和水杯位置的随机性=======================================================================================
#====================================================================================全程训练======================================================================================
from __future__ import annotations
from PIL import Image
import torch
torch.cuda.empty_cache()
import PyKDL as kdl
# from urdf_parser_py.urdf import URDF
# from pykdl_utils.kdl_parser import kdl_tree_from_urdf_model
import numpy as np

from isaacsim.core.utils.stage import get_current_stage
from isaacsim.core.utils.torch.transformations import tf_combine, tf_inverse, tf_vector
from pxr import UsdGeom

import isaaclab.sim as sim_utils
from isaaclab.actuators.actuator_cfg import ImplicitActuatorCfg
from isaaclab.assets import Articulation, ArticulationCfg, AssetBaseCfg, AssetBase, RigidObjectCfg, RigidObject
from isaaclab.envs import DirectRLEnv, DirectRLEnvCfg
from isaaclab.scene import InteractiveSceneCfg
from isaaclab.sim import SimulationCfg
from isaaclab.terrains import TerrainImporterCfg
from isaaclab.utils import configclass
from isaaclab.utils.assets import ISAAC_NUCLEUS_DIR
from isaaclab.utils.math import sample_uniform
from isaaclab.sensors import TiledCameraCfg,TiledCamera,ContactSensorCfg, ContactSensor
import isaaclab.utils.math as math_utils
import math
from isaaclab.utils.math import sample_uniform

from .ObjectCfg import Table_CFG, Kettle_CFG, Cup_CFG, QiruiRobot_CFG, Camera_CFG
from copy import deepcopy
import time

# 环境配置类
@configclass
class QiruiEnvCfg(DirectRLEnvCfg):
    # Environment Configuration
    episode_length_s: float = 20.0    # 迭代长度
    decimation: int = 4          
    action_space: int = 7 * 2          #  动作空间大小：双臂运动
    observation_space: int = 4 * 96 * 256  + 7 * 2 + 7*4  + 7      # 观测空间大小：图像（RGBD） + 手臂的关节角度值 + 手臂的关节速度值  + 手臂的末端位置 + Flag指标数
    state_space: int = 0  # Corrected to 0 to avoid negative dimensions
    debug_vis: bool = True         # 是否可显示

    # Simulation Configuration
    sim: SimulationCfg = SimulationCfg(
        dt=1 / 150,
        render_interval=decimation,
        #disable_contact_processing=True,
        physics_material=sim_utils.RigidBodyMaterialCfg(
            friction_combine_mode="multiply",
            restitution_combine_mode="multiply",
            static_friction=1.0,
            dynamic_friction=1.0,
            restitution=0.0,
        ),
    )

    # Scene Configuration
    scene: InteractiveSceneCfg = InteractiveSceneCfg(num_envs=15, env_spacing=3, replicate_physics=True)  

    # Robot Configuration

    # articulation
    Qiruirobot: ArticulationCfg = QiruiRobot_CFG.replace(prim_path="/World/envs/env_.*/QiruiRobot")   # 机器人配置

    Table = Table_CFG.replace(prim_path="/World/envs/env_.*/Table")   # 桌子配置
 
    Kettle: RigidObjectCfg = Kettle_CFG.replace(prim_path="/World/envs/env_.*/Kettle")    # 水壶配置

    Cup: RigidObjectCfg = Cup_CFG.replace(prim_path="/World/envs/env_.*/Cup")      # 水杯配置

    camera: TiledCameraCfg = Camera_CFG.replace(prim_path="/World/envs/env_.*/QiruiRobot/front_cam")     # 相机配置


    contact_forces_left_hand_intermediate = ContactSensorCfg(
    prim_path="/World/envs/env_.*/QiruiRobot/left_hand_.*_intermediate", update_period=0.0, history_length=6, debug_vis=True, filter_prim_paths_expr=["/World/envs/env_.*/Cup/cup"], 
    )   # 接触传感器配置（左手）

    contact_forces_right_hand_intermediate = ContactSensorCfg(
        prim_path="/World/envs/env_.*/QiruiRobot/right_hand_.*_intermediate", update_period=0.0, history_length=6, debug_vis=True, filter_prim_paths_expr=["/World/envs/env_.*/Kettle/bottle"],
    )   # 接触传感器配置（右手）

    # contact_sensor_table: ContactSensorCfg = ContactSensorCfg(
    #     prim_path=f"/World/envs/env_.*/Table/table", update_period=0.0, history_length=6, debug_vis=True, filter_prim_paths_expr= "/World/envs/env_.*/QiruiRobot/.*(hand|wrist|arm).*",
    # )

    action_scale = 0.5    # 动作的最大量

    # thrust_scale: float = 10.0  
    # angle_limit: float = math.pi / 4  

    # Reward Scales
    # ang_vel_reward_scale: float = -0.05
    # distance_to_goal_reward_scale: float = -0.1
    # orientation_penalty_scale: float = -0.1
    # joint_limit_penalty_scale: float = -1

class QiruiEnv(DirectRLEnv):
    # pre-physics step calls
    #   |-- _pre_physics_step(action)
    #   |-- _apply_action()
    # post-physics step calls
    #   |-- _get_dones()
    #   |-- _get_rewards()
    #   |-- _reset_idx(env_ids)
    #   |-- _get_observations()

    cfg: QiruiEnvCfg

    def __init__(self, cfg: QiruiEnvCfg, render_mode: str | None = None, **kwargs):
        super().__init__(cfg, render_mode, **kwargs)
        self.robot_root_pos = self._robot.data.root_pos_w
        self.left_arm_indices = self._robot.find_joints('.*left_arm_joint.*')[0]
        # self.left_arm_indices = [2, 6, 10, 14, 18, 22, 24]
        self.right_arm_indices = self._robot.find_joints('.*right_arm_joint.*')[0]
        # self.right_arm_indices = [3, 7, 11, 15, 19, 23, 25]
        self.left_hand_indices = self._robot.find_joints('left_hand.*joint')[0]
        # self.left_hand_indices = list(range(26, 31)) + list(range(36, 41)) + [46, 48]
        self.right_hand_indices = self._robot.find_joints('right_hand.*joint')[0]
        # self.right_hand_indices = list(range(31, 36)) + list(range(41, 46)) + [47, 49]
        self.kettle_orinal_pos = self._kettle.data.root_pos_w.clone()    # 水杯初始位置
        self.kettle_orinal_quat = self._kettle.data.root_quat_w.clone()   # 水杯初始姿态
        self.kettle_change_pos = self._kettle.data.root_pos_w.clone()    # 水杯位置

        self.cup_orinal_pos = self._cup.data.root_pos_w.clone()    # 水壶初始位置
        self.cup_orinal_quat = self._cup.data.root_quat_w.clone()  # 水壶初始姿态
        self.cup_change_pos = self._cup.data.root_pos_w.clone()    # 水壶初始位置

        self.table_orinal_pos = self._table.data.root_pos_w.clone()    # 桌子初始位置
        self.table_orinal_quat = self._table.data.root_quat_w.clone()  # 桌子初始姿态
         
        self.left_link7_idx = self._robot.find_bodies("left_arm_link07")[0][0]    # 左手掌的索引
        self.right_link7_idx = self._robot.find_bodies("right_arm_link07")[0][0]   # 右手掌的索引

        kettle_pos = self._kettle.data.root_com_pos_w.clone()      # 水壶中心位置
        self.kettle_z = kettle_pos[:,2].clone()
        kettle_pos[:,0] = kettle_pos[:,0] - 0.04
        kettle_pos[:,1] = kettle_pos[:,1] + 0.155
        kettle_pos[:,2] = kettle_pos[:,2] - 0.01           # 设置偏置
        cup_pos = self._cup.data.root_com_pos_w.clone()       # 水杯中心位置
        cup_pos[:,0] = cup_pos[:,0] + 0.0684 - 0.008           # 设置偏置
        cup_pos[:,1] = cup_pos[:,1] - 0.005 + 0.17 - 0.022  
        cup_pos[:,2] = cup_pos[:,2] - 0.015         # 设置偏置


        left_link7_state  = self._robot.data.body_link_state_w[:,self.left_link7_idx,0:7]   # 左臂第7关节值
        right_link7_state  = self._robot.data.body_link_state_w[:,self.right_link7_idx,0:7]   # 左臂第7关节值

        self.k_r_d_cur = torch.norm(right_link7_state[:,:3] - kettle_pos, p=2, dim=-1)
        self.c_l_d_cur = torch.norm(left_link7_state[:,:3]  - cup_pos, p=2, dim=-1)

        target_rot_l = torch.tensor([0.5, 0.5, -0.5, -0.5],device=self.device).repeat((self.num_envs,1))
        quat_diff_l = math_utils.quat_mul(left_link7_state[:,3:7], math_utils.quat_conjugate(target_rot_l))
        self.rot_dist_l = 2.0 * torch.asin(torch.clamp(torch.norm(quat_diff_l[:, 0:3], p=2, dim=-1), max=1.0))

        target_rot_r = torch.tensor([0.5, -0.5, 0.5, -0.5],device=self.device).repeat((self.num_envs,1))
        quat_diff_r = math_utils.quat_mul(right_link7_state[:,3:7], math_utils.quat_conjugate(target_rot_r))
        self.rot_dist_r = 2.0 * torch.asin(torch.clamp(torch.norm(quat_diff_r[:, 0:3], p=2, dim=-1), max=1.0))

        self.origi_left_joint  = self._robot.data.joint_pos[:,self.left_arm_indices].clone()   # 关节变量
        self.origi_right_joint  = self._robot.data.joint_pos[:,self.right_arm_indices].clone()   # 关节变量

        self.pre_left_joint_p  = self._robot.data.joint_pos[:,self.left_arm_indices].clone()   # 上一个关节位置
        self.pre_right_joint_p  = self._robot.data.joint_pos[:,self.right_arm_indices].clone()   # 上一个关节位置

        self.pre_left_joint_v  = torch.zeros((self.num_envs,7),device=self.device)  # 上一个关节速度
        self.pre_right_joint_v  = torch.zeros((self.num_envs,7),device=self.device)  # 上一个关节速度

        self.detect_kettle_cup_move_t = torch.zeros((self.num_envs),device=self.device)

        self.region_k_r_d = torch.ones((self.num_envs),dtype=torch.float,device=self.device) 
        self.region_c_l_d = torch.ones((self.num_envs),dtype=torch.float,device=self.device) 
        self.region_rot_dist_r = torch.ones((self.num_envs),dtype=torch.float,device=self.device)
        self.region_rot_dist_l = torch.ones((self.num_envs),dtype=torch.float,device=self.device)

        # self.stable_grasp_t =  torch.zeros((self.num_envs,10),device=self.device)
        # self.stable_grasp_index =  torch.zeros((self.num_envs),dtype=torch.int, device=self.device)

        # self.success_l = torch.tensor(0.0, device=self.device)
        # self.try_n_l = torch.tensor(0.0, device=self.device)
        # self.succ_n_l = torch.tensor(0.0, device=self.device)

        # self.success_r = torch.tensor(0.0, device=self.device)
        # self.try_n_r = torch.tensor(0.0, device=self.device)
        # self.succ_n_r = torch.tensor(0.0, device=self.device)

        # self.step_target_l = torch.tensor(0.5, device=self.device)
        # self.step_target_r = torch.tensor(0.5, device=self.device)

        # self.left_hand_thumb_tip_idx = self._robot.find_bodies("left_hand_thumb_tip")[0][0]
        # self.left_hand_index_tip_idx = self._robot.find_bodies("left_hand_index_tip")[0][0]
        # self.left_hand_middle_tip_idx = self._robot.find_bodies("left_hand_middle_tip")[0][0]
        # self.left_hand_pinky_tip_idx = self._robot.find_bodies("left_hand_pinky_tip")[0][0]
        # self.left_hand_ring_tip_idx = self._robot.find_bodies("left_hand_ring_tip")[0][0]
        # self.right_hand_thumb_tip_idx = self._robot.find_bodies("right_hand_thumb_tip")[0][0]
        # self.right_hand_index_tip_idx = self._robot.find_bodies("right_hand_index_tip")[0][0]
        # self.right_hand_middle_tip_idx = self._robot.find_bodies("right_hand_middle_tip")[0][0]
        # self.right_hand_pinky_tip_idx = self._robot.find_bodies("right_hand_pinky_tip")[0][0]
        # self.right_hand_ring_tip_idx = self._robot.find_bodies("right_hand_ring_tip")[0][0]

        # left_hand_thumb_tip_pos = self._robot.data.body_com_pos_w[:,self.left_hand_thumb_tip_idx]
        # left_hand_index_tip_pos = self._robot.data.body_com_pos_w[:,self.left_hand_index_tip_idx]
        # left_hand_middle_tip_pos = self._robot.data.body_com_pos_w[:,self.left_hand_middle_tip_idx]
        # left_hand_pinky_tip_pos = self._robot.data.body_com_pos_w[:,self.left_hand_pinky_tip_idx]
        # left_hand_ring_tip_pos = self._robot.data.body_com_pos_w[:,self.left_hand_ring_tip_idx]
        # right_hand_thumb_tip_pos = self._robot.data.body_com_pos_w[:,self.right_hand_thumb_tip_idx]
        # right_hand_index_tip_pos = self._robot.data.body_com_pos_w[:,self.right_hand_index_tip_idx]
        # right_hand_middle_tip_pos = self._robot.data.body_com_pos_w[:,self.right_hand_middle_tip_idx]
        # right_hand_ring_tip_pos = self._robot.data.body_com_pos_w[:,self.right_hand_ring_tip_idx]  
        # right_hand_pinky_tip_pos = self._robot.data.body_com_pos_w[:,self.right_hand_pinky_tip_idx]

        # self.kettle_thumb_p = deepcopy(kettle_pos)
        # self.kettle_thumb_p[:,2] = self.kettle_thumb_p[:,2] + 0.02
        # self.kettle_thumb_p[:,1] = self.kettle_thumb_p[:,1] + 0.01
        # self.kettle_thumb_p[:,0] = self.kettle_thumb_p[:,0] + 0.02
        # self.kettle_index_p = deepcopy(kettle_pos)
        # self.kettle_index_p[:,2] = self.kettle_index_p[:,2] + 0.02
        # self.kettle_index_p[:,1] = self.kettle_index_p[:,1] - 0.05
        # self.kettle_middle_p = deepcopy(kettle_pos)
        # self.kettle_middle_p[:,2] = self.kettle_middle_p[:,2] + 0.00
        # self.kettle_middle_p[:,1] = self.kettle_middle_p[:,1] - 0.06
        # self.kettle_ring_p = deepcopy(kettle_pos)
        # self.kettle_ring_p[:,2] = self.kettle_ring_p[:,2] - 0.01
        # self.kettle_ring_p[:,1] = self.kettle_ring_p[:,1] - 0.05
        # self.kettle_pinky_p = deepcopy(kettle_pos)
        # self.kettle_pinky_p[:,2] = self.kettle_pinky_p[:,2] - 0.02
        # self.kettle_pinky_p[:,1] = self.kettle_pinky_p[:,1] - 0.04
        # self.k_r_d = torch.norm(self.kettle_thumb_p - right_hand_thumb_tip_pos, p=2, dim=-1) + torch.norm(self.kettle_index_p - right_hand_index_tip_pos, p=2, dim=-1) + torch.norm(
        # self.kettle_middle_p - right_hand_middle_tip_pos, p=2, dim=-1)+ torch.norm(self.kettle_ring_p - right_hand_ring_tip_pos, p=2, dim=-1) + torch.norm(self.kettle_pinky_p - right_hand_pinky_tip_pos, p=2, dim=-1) 
        

        # self.cup_thumb_p = deepcopy(cup_pos)
        # self.cup_thumb_p[:,2] = self.cup_thumb_p[:,2] + 0.02
        # self.cup_thumb_p[:,1] = self.cup_thumb_p[:,1] + 0.01
        # self.cup_thumb_p[:,0] = self.cup_thumb_p[:,0] - 0.02
        # self.cup_index_p = deepcopy(cup_pos)
        # self.cup_index_p[:,2] = self.cup_index_p[:,2] + 0.02
        # self.cup_index_p[:,1] = self.cup_index_p[:,1] - 0.05
        # self.cup_middle_p = deepcopy(cup_pos)
        # self.cup_middle_p[:,2] = self.cup_middle_p[:,2]
        # self.cup_middle_p[:,1] = self.cup_middle_p[:,1] - 0.06
        # self.cup_ring_p = deepcopy(cup_pos)
        # self.cup_ring_p[:,2] = self.cup_ring_p[:,2] - 0.01
        # self.cup_ring_p[:,1] = self.cup_ring_p[:,1] - 0.05
        # self.cup_pinky_p = deepcopy(cup_pos)
        # self.cup_pinky_p[:,2] = self.cup_pinky_p[:,2] - 0.02
        # self.cup_pinky_p[:,1] = self.cup_pinky_p[:,1] - 0.04
        # self.c_l_d = torch.norm(self.cup_thumb_p - left_hand_thumb_tip_pos, p=2, dim=-1) + torch.norm(self.cup_index_p - left_hand_index_tip_pos, p=2, dim=-1) + torch.norm(
        # self.cup_middle_p - left_hand_middle_tip_pos, p=2, dim=-1)+ torch.norm(self.cup_ring_p - left_hand_ring_tip_pos, p=2, dim=-1) + torch.norm(self.cup_pinky_p - left_hand_pinky_tip_pos, p=2, dim=-1) 


        # target_rot_l = torch.tensor([1.0, 0.0, 0.0, 0.0], device=self.device).repeat(self.num_envs, 1)
        # target_rot_r = torch.tensor([0.0, 0.0, 1.0, 0.0], device=self.device).repeat(self.num_envs, 1)
        # left_hand_base_rot = self._robot.data.body_quat_w[:,self.left_hand_base_link_idx]
        # right_hand_base_rot = self._robot.data.body_quat_w[:,self.right_hand_base_link_idx]

        # quat_diff_l = math_utils.quat_mul(left_hand_base_rot, math_utils.quat_conjugate(target_rot_l))
        # self.rot_dist_l = 2.0 * torch.asin(torch.clamp(torch.norm(quat_diff_l[:, 0:3], p=2, dim=-1), max=1.0))
        # quat_diff_r = math_utils.quat_mul(right_hand_base_rot, math_utils.quat_conjugate(target_rot_r))
        # self.rot_dist_r = 2.0 * torch.asin(torch.clamp(torch.norm(quat_diff_r[:, 0:3], p=2, dim=-1), max=1.0))


        self.action_scale = self.cfg.action_scale    # 动作尺度
        self.robot_dof_lower_limits = self._robot.data.soft_joint_pos_limits[0, :, 0].to(device=self.device)  # 机器人关节限制最小值
        self.robot_dof_upper_limits = self._robot.data.soft_joint_pos_limits[0, :, 1].to(device=self.device)   # 机器人关节限制最大值

        # print('default_joint_pos',self._robot.data.default_joint_pos[0])
        self.robot_dof_targets = torch.zeros((self.num_envs, self._robot.num_joints), device=self.device)     # 机器人关节原始位置
        # 关节索引
        print(self._robot.find_joints('.*'))

        # 手握紧各关节值
        self.grasp_hand_upper = self.robot_dof_upper_limits[self.left_hand_indices]
        self.grasp_hand_lower = self.robot_dof_lower_limits[self.left_hand_indices]
        self.grasp_hand_base = (self.grasp_hand_upper - self.grasp_hand_lower)/100.0
        print('robot_dof_lower_limits', self.robot_dof_lower_limits[self.left_hand_indices])
        print('robot_dof_upper_limits',self.robot_dof_upper_limits[self.left_hand_indices])

        # self.grasp_size = torch.zeros([self.num_envs,2], dtype=torch.float, device=self.device)

        self.flag = torch.tensor([0]*self.num_envs,device=self.device)



    # 建立场景
    def _setup_scene(self):
        self._robot = Articulation(self.cfg.Qiruirobot)
        self._kettle = RigidObject(self.cfg.Kettle)
        self._table = RigidObject(self.cfg.Table)
        self._cup = RigidObject(self.cfg.Cup)
        self._camera = TiledCamera(self.cfg.camera)
        self._contact_forces_left_hand_intermediate = ContactSensor(self.cfg.contact_forces_left_hand_intermediate)
        self._contact_forces_right_hand_intermediate= ContactSensor(self.cfg.contact_forces_right_hand_intermediate)
        # self._contact_sensor_table = ContactSensor(self.cfg.contact_sensor_table)
        # self._contact_forces_left_hand_index_proximal= ContactSensor(self.cfg.contact_forces_left_hand_index_proximal)
        # self._contact_forces_left_hand_middle_proximal= ContactSensor(self.cfg.contact_forces_left_hand_middle_proximal)
        # self._contact_forces_left_hand_pinky_proximal= ContactSensor(self.cfg.contact_forces_left_hand_pinky_proximal)
        # self._contact_forces_left_hand_ring_proximal= ContactSensor(self.cfg.contact_forces_left_hand_ring_proximal)
        # self._contact_forces_left_hand_thumb_proximal= ContactSensor(self.cfg.contact_forces_left_hand_thumb_proximal)

        # self._contact_forces_right_hand_index_proximal= ContactSensor(self.cfg.contact_forces_right_hand_index_proximal)
        # self._contact_forces_right_hand_middle_proximal= ContactSensor(self.cfg.contact_forces_right_hand_middle_proximal)
        # self._contact_forces_right_hand_pinky_proximal= ContactSensor(self.cfg.contact_forces_right_hand_pinky_proximal)
        # self._contact_forces_right_hand_ring_proximal= ContactSensor(self.cfg.contact_forces_right_hand_ring_proximal)
        # self._contact_forces_right_hand_thumb_proximal= ContactSensor(self.cfg.contact_forces_right_hand_thumb_proximal)
        
        self.scene.articulations["robot"] = self._robot
        self.scene.rigid_objects["kettle"] = self._kettle
        self.scene.rigid_objects["table"] = self._table
        self.scene.rigid_objects["cup"] = self._cup
        self.scene.sensors["camera"] = self._camera
        self.scene.sensors["contact_forces_left_hand_intermediate"] = self._contact_forces_left_hand_intermediate
        self.scene.sensors["contact_forces_right_hand_intermediate"] = self._contact_forces_right_hand_intermediate
        # self.scene.sensors["contact_sensor_table"] = self._contact_sensor_table

        # self.scene.sensors["contact_forces_left_hand_index_proximal"] = self._contact_forces_left_hand_index_proximal
        # self.scene.sensors["contact_forces_left_hand_middle_proximal"] = self._contact_forces_left_hand_middle_proximal
        # self.scene.sensors["contact_forces_left_hand_pinky_proximal"] = self._contact_forces_left_hand_pinky_proximal
        # self.scene.sensors["contact_forces_left_hand_ring_proximal"] = self._contact_forces_left_hand_ring_proximal
        # self.scene.sensors["contact_forces_left_hand_thumb_proximal"] = self._contact_forces_left_hand_thumb_proximal

        # self.scene.sensors["contact_forces_right_hand_index_proximal"] = self._contact_forces_right_hand_index_proximal
        # self.scene.sensors["contact_forces_right_hand_middle_proximal"] = self._contact_forces_right_hand_middle_proximal
        # self.scene.sensors["contact_forces_right_hand_pinky_proximal"] = self._contact_forces_right_hand_pinky_proximal
        # self.scene.sensors["contact_forces_right_hand_ring_proximal"] = self._contact_forces_right_hand_ring_proximal
        # self.scene.sensors["contact_forces_right_hand_thumb_proximal"] = self._contact_forces_right_hand_thumb_proximal

        # self.cfg.terrain.num_envs = self.scene.cfg.num_envs
        # self.cfg.terrain.env_spacing = self.scene.cfg.env_spacing
        # self._terrain = self.cfg.terrain.class_type(self.cfg.terrain)

        # clone, filter, and replicate
        self.scene.clone_environments(copy_from_source=False)
        self.scene.filter_collisions(global_prim_paths=[])

        # add lights
        light_cfg = sim_utils.DomeLightCfg(intensity=2000.0, color=(0.75, 0.75, 0.75))
        light_cfg.func("/World/Light", light_cfg)

        cfg_ground = sim_utils.GroundPlaneCfg(size=(2000,2000))
        cfg_ground.func("/World/defaultGroundPlane", cfg_ground)

    # pre-physics step calls
    # 手执行抓取和放开动作
    def _grasp(self, left_grasp_enable, right_grasp_enable):
        # #手张开
        # joint_lhand_loose = self._robot.data.soft_joint_pos_limits[:, self.left_hand_indices, 0]
        # self.robot_dof_targets[:, self.left_hand_indices] = joint_lhand_loose.clone()
        # joint_rhand_loose = self._robot.data.soft_joint_pos_limits[:, self.right_hand_indices, 0]
        # self.robot_dof_targets[:, self.right_hand_indices] = joint_rhand_loose.clone()
        self.robot_dof_targets[(~left_grasp_enable).nonzero(), self.left_hand_indices] = self.grasp_hand_lower
        self.robot_dof_targets[(~right_grasp_enable).nonzero(), self.right_hand_indices] = self.grasp_hand_lower

        #左手抓握
        self.robot_dof_targets[left_grasp_enable.nonzero(), self.left_hand_indices] = self.grasp_hand_upper * 0.35
        #右手抓握
        self.robot_dof_targets[right_grasp_enable.nonzero(), self.right_hand_indices] = self.grasp_hand_upper * 0.55

    # 动作前的函数
    def _pre_physics_step(self, actions: torch.Tensor):
        # 复位手的位置
        # left_grasp_enable = torch.zeros((self.num_envs), device=self.device, dtype=torch.bool)   
        # right_grasp_enable = torch.zeros((self.num_envs), device=self.device, dtype=torch.bool)
        # self._grasp(left_grasp_enable, right_grasp_enable)

        # term_left_hand = torch.flatten(self.c_l_d < 0.1)
        # term_right_hand = torch.flatten(self.k_r_d < 0.1)
        # self._grasp(term_left_hand, term_right_hand)  # 如果到达指定位置（距离小于0.1）则手抓紧、

        # 左臂右臂关节值赋值
        self.actions = actions.clone().clamp(-1.0, 1.0)
        self.robot_dof_targets = self._robot.data.joint_pos
        self._grasp((self.flag==1) | (self.flag==2),(self.flag==1) | (self.flag==2))
        # self.robot_dof_targets[:, self.left_hand_indices] = self.robot_dof_targets[:,self.left_hand_indices] + self.actions[:,14:15] * self.grasp_hand_base
        # self.robot_dof_targets[:, self.right_hand_indices] = self.robot_dof_targets[:,self.right_hand_indices] + self.actions[:,15:16] * self.grasp_hand_base
        self.robot_dof_targets[:, self.left_arm_indices + self.right_arm_indices] = self.robot_dof_targets[:,self.left_arm_indices+self.right_arm_indices] + self.actions[:,:14] * self.action_scale   # 给左臂和右臂赋值
        
        targets = self.robot_dof_targets
        self.robot_dof_targets[:] = torch.clamp(targets, self.robot_dof_lower_limits, self.robot_dof_upper_limits)
    
    # 执行动作
    def _apply_action(self):
        # 驱动关节
        self._robot.set_joint_position_target(self.robot_dof_targets)

    # post-physics step calls
    # 判断回合有没有结束
    def _get_dones(self) -> tuple[torch.Tensor, torch.Tensor]:
        print('self.flag',self.flag)
#=========================================================计算第一阶段的终止条件==========================================================================
        kettle_pos = self._kettle.data.root_com_pos_w.clone()      # 水壶中心位置
        kettle_pos[:,0] = kettle_pos[:,0] - 0.04
        kettle_pos[:,1] = kettle_pos[:,1] + 0.155
        kettle_pos[:,2] = kettle_pos[:,2] - 0.01           # 设置偏置
        cup_pos = self._cup.data.root_com_pos_w.clone()       # 水杯中心位置
        cup_pos[:,0] = cup_pos[:,0] + 0.0684 - 0.008           # 设置偏置
        cup_pos[:,1] = cup_pos[:,1] - 0.005 + 0.17 - 0.022  
        cup_pos[:,2] = cup_pos[:,2] - 0.015         # 设置偏置

        left_link7_state  = self._robot.data.body_link_state_w[:,self.left_link7_idx,0:7]   # 左臂第7关节值
        right_link7_state  = self._robot.data.body_link_state_w[:,self.right_link7_idx,0:7]   # 左臂第7关节值

        self.k_r_d_cur[(self.flag == 0) | (self.flag == 1)] = torch.norm(right_link7_state[:,:3] - kettle_pos, p=2, dim=-1)[(self.flag == 0) | (self.flag == 1)]
        self.c_l_d_cur[(self.flag == 0) | (self.flag == 1)] = torch.norm(left_link7_state[:,:3]  - cup_pos, p=2, dim=-1)[(self.flag == 0) | (self.flag == 1)]

        target_rot_l = torch.tensor([0.5, 0.5, -0.5, -0.5],device=self.device).repeat((self.num_envs,1))
        quat_diff_l1 = math_utils.quat_mul(left_link7_state[:,3:7], math_utils.quat_conjugate(target_rot_l))
        self.rot_dist_l[(self.flag == 0) | (self.flag == 1)] = 2.0 * torch.asin(torch.clamp(torch.norm(quat_diff_l1[:, 1:4], p=2, dim=-1), 0.0,1.0))[(self.flag == 0) | (self.flag == 1)]

        target_rot_r = torch.tensor([0.5, -0.5, 0.5, -0.5],device=self.device).repeat((self.num_envs,1))
        quat_diff_r1 = math_utils.quat_mul(right_link7_state[:,3:7], math_utils.quat_conjugate(target_rot_r))
        self.rot_dist_r[(self.flag == 0) | (self.flag == 1)] = 2.0 * torch.asin(torch.clamp(torch.norm(quat_diff_r1[:, 1:4], p=2, dim=-1), 0.0,1.0))[(self.flag == 0) | (self.flag == 1)]

#==========判断flag====================
        self.target_left_hand1 = torch.flatten(self.c_l_d_cur < 0.018)    # 判断是否到达
        self.target_right_hand1 = torch.flatten(self.k_r_d_cur < 0.018)
        self.target_left_rot1 = torch.flatten(self.rot_dist_l < 0.08)    # 判断角度是否将近重合
        self.target_right_rot1 = torch.flatten(self.rot_dist_r < 0.08)
        # self.stable_grasp_t[torch.nonzero(self.target_left_hand1 & self.target_right_hand1 & self.target_left_rot1 & self.target_right_rot1),self.stable_grasp_index] = 1
        # self.stable_grasp_t[torch.nonzero(~(self.target_left_hand1 & self.target_right_hand1 & self.target_left_rot1 & self.target_right_rot1)),self.stable_grasp_index] = 0
        # self.stable_grasp_index = (self.stable_grasp_index+1) % 10  
        # print('self.stable_grasp_t',self.stable_grasp_t[:3,:])
        self.flag[(self.target_left_hand1 & self.target_right_hand1 & self.target_left_rot1 & self.target_right_rot1) & (self.flag == 0)] = 1  # 计算flag 
        # self.flag[(~(self.target_left_hand1 & self.target_right_hand1 & self.target_left_rot1 & self.target_right_rot1)) & (self.flag == 1)] = 0  # 计算flag 

        # 杯子、水壶以及桌子是否挪动
        self.detect_kettle_cup_move_t = self.detect_kettle_cup_move_t + 1
        kettle_root_pos = self._kettle.data.root_pos_w
        cup_root_pos = self._cup.data.root_pos_w
        self.kettle_change_pos[self.detect_kettle_cup_move_t == 40] = kettle_root_pos[self.detect_kettle_cup_move_t == 40]
        self.cup_change_pos[self.detect_kettle_cup_move_t == 40] = cup_root_pos[self.detect_kettle_cup_move_t == 40]
        self.k_change = torch.any(torch.abs(kettle_root_pos[:,:2] - self.kettle_change_pos[:,:2]) > 0.001, dim=1) & (self.detect_kettle_cup_move_t > 40)
        self.c_change = torch.any(torch.abs(cup_root_pos[:,:2] - self.cup_change_pos[:,:2]) > 0.001, dim=1) & (self.detect_kettle_cup_move_t > 40)
        # k_fall = kettle_pos[:,2] < 0.1
        self.k_change = self.k_change | ((self._kettle.data.root_com_pos_w[:,2] < (self.kettle_z-0.005)) & (self.detect_kettle_cup_move_t == 40))
        # c_fall = cup_pos[:,2] < 0.1
        k_change = self.k_change.unsqueeze(1)
        c_change = self.c_change.unsqueeze(1)
        table_root_pos = self._table.data.root_pos_w
        self.term_arm_hand = torch.any(torch.abs(table_root_pos - self.table_orinal_pos) > 0.002, dim=1)
        term_arm_hand = self.term_arm_hand.unsqueeze(1)
        # if torch.any(term_arm_hand):
        #     print('term_arm_hand  ',term_arm_hand)

        terminated_0 = torch.any(torch.concat((k_change, c_change,term_arm_hand), dim=1),dim=1) & (self.flag == 0)
        # term_left_hand = self.term_left_hand.unsqueeze(1) & self.term_left_rot.unsqueeze(1)
        # term_right_hand = self.term_right_hand.unsqueeze(1) & self.term_right_rot.unsqueeze(1)

#=========================================================计算第二阶段的终止条件==========================================================================
        self.flag_1to2 = torch.tensor([False]*self.num_envs,device=self.device)  # 是否抓取成功
        # 抓取成功否判断
        threthod_force = 2
        term_left_hand_grasp = torch.tensor([False]*self.num_envs,device=self.device)
        term_right_hand_grasp = torch.tensor([False]*self.num_envs,device=self.device)
        if self._contact_forces_left_hand_intermediate.data.net_forces_w != None:
            term_left_hand_grasp_1 = torch.any(torch.abs(self._contact_forces_left_hand_intermediate.data.net_forces_w) > threthod_force, dim=2)
            term_left_hand_grasp = torch.sum(term_left_hand_grasp_1,dim=1) >= 2
            print('contact_Force',self._contact_forces_left_hand_intermediate.data.net_forces_w[self.flag == 1])
        if self._contact_forces_right_hand_intermediate.data.net_forces_w != None:
            term_right_hand_grasp1 = torch.any(torch.abs(self._contact_forces_right_hand_intermediate.data.net_forces_w) > threthod_force, dim=2)
            term_right_hand_grasp = torch.sum(term_right_hand_grasp1,dim=1) >= 2
        self.flag_1to2[term_left_hand_grasp & term_right_hand_grasp & (self.flag == 1)] = True
        self.flag[term_left_hand_grasp & term_right_hand_grasp & (self.flag == 1)] = 2

        self.region_k_r_d[self.flag_1to2] = torch.ones((int(torch.sum(self.flag_1to2))),dtype=torch.float,device=self.device)
        self.region_c_l_d[self.flag_1to2] = torch.ones((int(torch.sum(self.flag_1to2))),dtype=torch.float,device=self.device)
        self.region_rot_dist_r[self.flag_1to2] = torch.ones((int(torch.sum(self.flag_1to2))),dtype=torch.float,device=self.device)
        self.region_rot_dist_l[self.flag_1to2] = torch.ones((int(torch.sum(self.flag_1to2))),dtype=torch.float,device=self.device)
        


#=========================================================计算第三阶段的终止条件==========================================================================
        self.flag_2to3 = torch.tensor([False]*self.num_envs,device=self.device)  # 是否抓取成功
        pour_water_target_l = torch.tensor([0.1106, 0.4530, 1.25, 0.5, 0.5, -0.5, -0.5], device=self.device)
        pour_water_target_r = torch.tensor([-0.2151,  0.4530,  1.3, 0.2706, -0.6533, 0.6533, -0.2706], device=self.device)
        robot_base_pos = torch.tensor([-1.4552e-11,  8.0000e-01,  1.1000e+00], device=self.device)

        self.k_r_d_cur[self.flag == 2] = torch.norm(right_link7_state[:,:3] - (self.robot_root_pos + pour_water_target_r[:3]- robot_base_pos), p=2, dim=-1)[self.flag == 2]
        self.c_l_d_cur[self.flag == 2] = torch.norm(left_link7_state[:,:3]  - (self.robot_root_pos + pour_water_target_l[:3]- robot_base_pos), p=2, dim=-1)[self.flag == 2]

        quat_diff_l2 = math_utils.quat_mul(left_link7_state[:,3:7], math_utils.quat_conjugate(target_rot_l))
        self.rot_dist_l[self.flag == 2] = 2.0 * torch.asin(torch.clamp(torch.norm(quat_diff_l2[:, 1:4], p=2, dim=-1), 0.0,1.0))[self.flag == 2]
        target_rot_r2 = torch.tensor([0.2706, -0.6533, 0.6533, -0.2706],device=self.device).repeat((self.num_envs,1))
        quat_diff_r2 = math_utils.quat_mul(right_link7_state[:,3:7], math_utils.quat_conjugate(target_rot_r2))
        self.rot_dist_r[self.flag == 2] = 2.0 * torch.asin(torch.clamp(torch.norm(quat_diff_r2[:, 1:4], p=2, dim=-1), 0.0,1.0))[self.flag == 2]
        self.target_left_hand2 = torch.flatten(self.c_l_d_cur < 0.02)    # 判断是否到达
        self.target_right_hand2 = torch.flatten(self.k_r_d_cur < 0.02)
        self.target_left_rot2 = torch.flatten(self.rot_dist_l < 0.05)    # 判断角度是否将近重合
        self.target_right_rot2 = torch.flatten(self.rot_dist_r < 0.05)

        terminated_2 = self.target_left_hand2 & self.target_right_hand2 & self.target_left_rot2 & self.target_right_rot2 & (self.flag == 2)
        self.flag_2to3[terminated_2] = True



        terminated = terminated_0 | self.flag_1to2 #terminated_2
        # print("terminated",terminated)
        truncated = self.episode_length_buf >= self.max_episode_length - 1

        self.end = terminated | truncated

        # self.try_n_l += len(self.end.nonzero())
        # self.try_n_r += len(self.end.nonzero())
        # self.succ_n_l += len(torch.flatten(self.c_l_d_cur < self.step_target_l).nonzero())
        # self.succ_n_r += len(torch.flatten(self.k_r_d_cur < self.step_target_r).nonzero())

        # if self.try_n_l == 100:
        #     self.success_l = self.succ_n_l/self.try_n_l
        #     self.try_n_l = 0.0
        #     self.succ_n_l = 0.0
        #     if self.success_l > 0.8:
        #         self.step_target_l = max(0.02, self.step_target_l*0.95)
        #         self.success_l = 0.0

        # if self.try_n_r == 100:
        #     self.success_r = self.succ_n_r/self.try_n_r
        #     self.try_n_r = 0.0
        #     self.succ_n_r = 0.0
        #     if self.success_r > 0.8:
        #         self.step_target_r = max(0.02, self.step_target_r*0.95)
        #         self.success_r = 0.0


        # print("truncated",truncated)
        if torch.any(terminated):
            print('terminated',terminated)
            print('k_change',k_change)
            print('c_change',c_change)
            print('term_arm_hand',term_arm_hand)
        return terminated, truncated

    # 奖励函数
    # def _get_rewards(self) -> torch.Tensor:

    #     # left_hand_base_rot = self._robot.data.body_quat_w[:,self.left_hand_base_link_idx]
    #     # right_hand_base_rot = self._robot.data.body_quat_w[:,self.right_hand_base_link_idx])

    # # ------------------------------  approach rewarnd --------------------------------------------------
    #     # distance from hand to the drawer
    #     # dist_reward_r = torch.where(self.k_r_d_cur < self.k_r_d, 0.2, -0.2)
    #     # dist_reward_r =  torch.exp(-self.k_r_d_cur)
    #     dist_reward_r =  torch.clamp(1.0 / self.k_r_d_cur / 10.0, max = 5.0)
    #     # print("self.k_r_d_cur < self.k_r_d ",self.k_r_d_cur < self.k_r_d )
    #     print("k_r_d ",self.k_r_d_cur)
    #     # print('dist_reward_r',dist_reward_r)
    #     # dist_reward_l = torch.where(self.c_l_d_cur < self.c_l_d, 0.2, -0.2)
    #     # dist_reward_l =  torch.exp(-self.c_l_d_cur)
    #     dist_reward_l =  torch.clamp(1.0 / self.c_l_d_cur / 10.0,max = 5.0)
    #     print("c_l_d ", self.c_l_d_cur)

    #     approach_dist_reward_l = torch.where(self.term_left_hand, 1, 0)
    #     approach_dist_reward_r = torch.where(self.term_right_hand, 1, 0)

    # # ------------------------------  grasp rewarnd --------------------------------------------------
    #     # grasp_reward_l = torch.where(self.term_left_hand_grasp, 10, 0)
    #     # grasp_reward_r = torch.where(self.term_right_hand_grasp, 10, 0)

    # # ------------------------------  quat rewarnd --------------------------------------------------
    #     # rot_reward_l = torch.exp(-self.rot_dist_l/3.0)
    #     # rot_reward_r = torch.exp(-self.rot_dist_r/3.0)
    #     rot_reward_l = torch.clamp(1.0 / self.rot_dist_l / 4.0, max = 5.0)
    #     rot_reward_r = torch.clamp(1.0 / self.rot_dist_r / 4.0, max = 5.0)
    #     print("rot_dist_l",self.rot_dist_l)
    #     print("rot_dist_r",self.rot_dist_r)

    #     # print('self.step_target_l',self.step_target_l)
    #     # print('self.step_target_r',self.step_target_r)

    #     approach_rot_reward_l = torch.where(self.term_left_rot, 1, 0)
    #     approach_rot_reward_r = torch.where(self.term_right_rot, 1, 0)

    # # ------------------------------  penalty   --------------------------------------------------
    #     term_arm_hand_penalty = torch.where(self.term_arm_hand, -0.5, 0)
    #     k_change_penalty = torch.where(self.k_change, -0.5, 0)
    #     c_change_penalty = torch.where(self.c_change, -0.5, 0)

    #     cur_left_joint  = self._robot.data.joint_pos[:,self.left_arm_indices].clone()   # 关节变量
    #     cur_right_joint  = self._robot.data.joint_pos[:,self.right_arm_indices].clone()   # 关节变量
    #     l_joint_diff = torch.norm(cur_left_joint - self.origi_left_joint, p=2, dim=-1)
    #     r_joint_diff = torch.norm(cur_right_joint - self.origi_right_joint, p=2, dim=-1)
    #     joint_diff_penalty = torch.where(self.end, -0.2*l_joint_diff-0.2*r_joint_diff, 0)
    #     # print('joint_diff_penalty',joint_diff_penalty)
        
    # # ------------------------------  Final reward   --------------------------------------------------
    #     dist_threshold = 0.3
    #     rewards = torch.zeros_like(dist_reward_r)
    #     # penalty_index = self.term_arm_hand | self.k_change | self.c_change
    #     # rewards[penalty_index] += 0.4* (k_change_penalty[penalty_index] + c_change_penalty[penalty_index] + term_arm_hand_penalty[penalty_index])

    #     # rewards[(self.k_r_d_cur >= dist_threshold) & (~penalty_index)] += 0.5 * dist_reward_r[(self.k_r_d_cur >= dist_threshold) & (~penalty_index)]
    #     # rewards[(self.k_r_d_cur < dist_threshold) & (~penalty_index)] += 0.5*(0.5 * dist_reward_r[(self.k_r_d_cur < dist_threshold) & (~penalty_index)] + 0.5 * rot_reward_r[(self.k_r_d_cur < dist_threshold) & (~penalty_index)])
    #     # rewards[(self.c_l_d_cur >= dist_threshold) & (~penalty_index)] += 0.5 * dist_reward_l[(self.c_l_d_cur >= dist_threshold) & (~penalty_index)]
    #     # rewards[(self.c_l_d_cur < dist_threshold) & (~penalty_index)] += 0.5*(0.5 * dist_reward_l[(self.c_l_d_cur < dist_threshold) & (~penalty_index)] + 0.5 * rot_reward_l[(self.c_l_d_cur < dist_threshold) & (~penalty_index)])
    #     # # print('rewards',rewards)
    #     # rewards += 0.5 * (joint_diff_penalty)


    #     rewards[self.k_r_d_cur >= dist_threshold] += 0.5 * dist_reward_r[self.k_r_d_cur >= dist_threshold]
    #     rewards[self.k_r_d_cur < dist_threshold] += 0.5*(0.5 * dist_reward_r[self.k_r_d_cur < dist_threshold] + 0.5 * rot_reward_r[self.k_r_d_cur < dist_threshold])
    #     rewards[self.c_l_d_cur >= dist_threshold] += 0.5 * dist_reward_l[self.c_l_d_cur >= dist_threshold]
    #     rewards[self.c_l_d_cur < dist_threshold] += 0.5*(0.5 * dist_reward_l[(self.c_l_d_cur < dist_threshold)] + 0.5 * rot_reward_l[self.c_l_d_cur < dist_threshold])
    #     # print('rewards',rewards)
    #     rewards += 0.5 * (k_change_penalty + c_change_penalty + term_arm_hand_penalty  + joint_diff_penalty)


    #     # rewards = 0.5 * (dist_reward_r + dist_reward_l) + 0.4 * (rot_reward_l) + 0.4 * (rot_reward_r) + 0.3 * (k_change_penalty + c_change_penalty + 
    #     #                  term_arm_hand_penalty  + joint_diff_penalty)      
        
    #     # rewards = 0.5 * (dist_reward_r + dist_reward_l
    #     #                  ) + (torch.exp(-self.c_l_d_cur * 1.2)*0.6) * (rot_reward_l) + (torch.exp(-self.k_r_d_cur * 1.2)*0.6) * (rot_reward_r) + 0.5 * (k_change_penalty + c_change_penalty + 
    #     #                  term_arm_hand_penalty  + joint_diff_penalty)
 

    #     #  + approach_dist_reward_l +  approach_dist_reward_r + approach_rot_reward_l + approach_rot_reward_r

        # return rewards
    def _get_rewards(self) -> torch.Tensor:
        # ------------------------------ 位置和姿态的区间划分 --------------------------------------------------

        # 将指标归一化到[0, 1]之间
        self.max_k_r_d = 1.0
        self.max_c_l_d = 1.0
        self.max_rot_dist_r = 2.0
        self.max_rot_dist_l = 2.0
        norm_k_r_d_cur = torch.clamp(self.k_r_d_cur / self.max_k_r_d, 0, 1)
        norm_c_l_d_cur = torch.clamp(self.c_l_d_cur / self.max_c_l_d, 0, 1)
        norm_rot_dist_r = torch.clamp(self.rot_dist_r / self.max_rot_dist_r, 0, 1)
        norm_rot_dist_l = torch.clamp(self.rot_dist_l / self.max_rot_dist_l, 0, 1)


        print("k_r_d ",self.k_r_d_cur)
        print("c_l_d ", self.c_l_d_cur)
        print("rot_dist_l",self.rot_dist_l)
        print("rot_dist_r",self.rot_dist_r)

        # ------------------------------ 支配关系 --------------------------------------------------
        # 定义一个函数判断当前指标是否支配了前一个指标
        def dominates(new_region, prev_region):
            return torch.all(new_region <= prev_region, dim = 1)

        # ------------------------------ 联合奖励 --------------------------------------------------
        # 初始化奖励
        rewards = torch.zeros_like(norm_k_r_d_cur, dtype=torch.float, device=self.device)

        # 只有新的指标支配了上一个指标时，才给奖励
        cur_index = torch.cat((norm_k_r_d_cur.unsqueeze(1),norm_c_l_d_cur.unsqueeze(1),norm_rot_dist_r.unsqueeze(1),norm_rot_dist_l.unsqueeze(1)),dim=1)
        last_index = torch.cat((self.region_k_r_d.unsqueeze(1),self.region_c_l_d.unsqueeze(1),self.region_rot_dist_r.unsqueeze(1),self.region_rot_dist_l.unsqueeze(1)),dim=1)
        condition_d = dominates(cur_index, last_index) 
        condition_t = (torch.mean(cur_index,dim=1) < 0.15) # | (self.flag == 2)

        self.region_k_r_d= norm_k_r_d_cur.clone()
        self.region_c_l_d = norm_c_l_d_cur.clone()
        self.region_rot_dist_r = norm_rot_dist_r.clone()
        self.region_rot_dist_l = norm_rot_dist_l.clone()

        # print(condition_d,'condition_d')

        # print('cur_index',cur_index)


        # 计算最终奖励
        rewards[condition_d & (~condition_t)] = 1.0 / (torch.mean(cur_index, dim=1)[condition_d & (~condition_t)] + 0.02) / 3.0  # 只有条件满足时，才给正奖励
        rewards[condition_t] = 1.0 / (torch.sum(cur_index * torch.tensor([0.35,0.35,0.15,0.15],device=self.device), dim=1)[condition_t] + 0.02) / 3.0
        rewards[self.flag == 1] = 50.0
        rewards[self.flag_1to2] = 50.0
        rewards[self.flag == 2] += 50.0
        rewards[self.flag_2to3] = 100.0
        # print('rewards',rewards)
        # print('     ')
        print('     ')


        # ------------------------------ 惩罚项 --------------------------------------------------
        term_arm_hand_penalty = torch.where(self.term_arm_hand, -0.5, 0)
        k_change_penalty = torch.where(self.k_change, -0.5, 0)
        c_change_penalty = torch.where(self.c_change, -0.5, 0)

        # cur_left_joint = self._robot.data.joint_pos[:, self.left_arm_indices].clone()
        # cur_right_joint = self._robot.data.joint_pos[:, self.right_arm_indices].clone()
        # l_joint_diff = torch.norm(cur_left_joint - self.origi_left_joint, p=2, dim=-1)
        # r_joint_diff = torch.norm(cur_right_joint - self.origi_right_joint, p=2, dim=-1)
        # joint_diff_penalty = torch.where(self.end, -0.2 * l_joint_diff - 0.2 * r_joint_diff, 0)

        # ------------------------------ 最终奖励 --------------------------------------------------
        rewards += 0.5 * (
            k_change_penalty + c_change_penalty + term_arm_hand_penalty
        )

        return rewards

    # 复原函数
    def _reset_idx(self, env_ids: torch.Tensor | None):
        if env_ids is None:
            env_ids = self._robot._ALL_INDICES
        super()._reset_idx(env_ids)
        # robot state
        # joint_pos = self._robot.data.default_joint_pos[env_ids]
        joint_target_pos = torch.zeros((self.num_envs, self._robot.num_joints), device=self.device) 
        joint_pos = joint_target_pos[env_ids,:]

        joint_pos = torch.clamp(joint_pos, self.robot_dof_lower_limits, self.robot_dof_upper_limits)
        
        joint_vel = torch.zeros_like(joint_pos)
        self._robot.set_joint_position_target(joint_pos, env_ids=env_ids)
        self._robot.write_joint_state_to_sim(joint_pos, joint_vel, env_ids=env_ids)
        kettle_orinal_pos = self.kettle_orinal_pos[env_ids, :].clone() 
        cup_orinal_pos = self.cup_orinal_pos[env_ids, :].clone()

        kettle_orinal_pos[:, 0]  = kettle_orinal_pos[:, 0] + sample_uniform(
            -0.03,
            0.03,
            len(env_ids),
            self.device,
        )
        kettle_orinal_pos[:, 1] = kettle_orinal_pos[:, 1] + sample_uniform(
            0.00,
            0.06,
            len(env_ids),
            self.device,
        )

        cup_orinal_pos[:, 0] = cup_orinal_pos[:, 0] + sample_uniform(
            -0.02,
            0.04,
            len(env_ids),
            self.device,
        )
        cup_orinal_pos[:, 1] = cup_orinal_pos[:, 1] + sample_uniform(
            -0.03,
            0.03,
            len(env_ids),
            self.device,
        )
        
        self._kettle.write_root_pose_to_sim(torch.cat((kettle_orinal_pos,self.kettle_orinal_quat[env_ids,:]),dim=-1), env_ids)
        self._cup.write_root_pose_to_sim(torch.cat((cup_orinal_pos,self.cup_orinal_quat[env_ids,:]),dim=-1), env_ids)
        self._table.write_root_pose_to_sim(torch.cat((self.table_orinal_pos[env_ids,:],self.table_orinal_quat[env_ids,:]),dim=-1), env_ids)
        # self.kettle_change_pos[env_ids] = kettle_orinal_pos
        # self.cup_change_pos[env_ids] = cup_orinal_pos
        
        # print('env_ids',env_ids)
        # self._contact_forces_left_hand_proximal.reset()
        # self._contact_forces_right_hand_proximal.reset()
        # self._contact_sensor_table.reset()
        self.pre_left_joint_p[env_ids]  = torch.zeros((len(env_ids), 7), device=self.device)    # 上一个关节位置
        self.pre_right_joint_p[env_ids]   = torch.zeros((len(env_ids), 7), device=self.device)   # 上一个关节位置

        self.pre_left_joint_v[env_ids]  = torch.zeros((len(env_ids), 7), device=self.device)  # 上一个关节速度
        self.pre_right_joint_v[env_ids]  = torch.zeros((len(env_ids), 7), device=self.device) # 上一个关节速度

        self.detect_kettle_cup_move_t[env_ids] = 0

        self.region_k_r_d[env_ids] = torch.ones((len(env_ids)),dtype=torch.float,device=self.device)
        self.region_c_l_d[env_ids] = torch.ones((len(env_ids)),dtype=torch.float,device=self.device)
        self.region_rot_dist_r[env_ids] = torch.ones((len(env_ids)),dtype=torch.float,device=self.device)
        self.region_rot_dist_l[env_ids] = torch.ones((len(env_ids)),dtype=torch.float,device=self.device)

        self.flag[env_ids] = 0

    # 获取观测值函数
    def _get_observations(self) -> dict:

        camera_data_rgb = self._camera.data.output["rgb"] / 255.0

        camera_data_rgb = camera_data_rgb[:,124:220,:,:]

        # normalize the camera data for better training results
        mean_tensor = torch.mean(camera_data_rgb, dim=(1, 2), keepdim=True)
        camera_data_rgb -= mean_tensor


        camera_data_depth = self._camera.data.output["depth"][:,124:220,:,:]
        camera_data_depth[camera_data_depth > float(2.0)] = 0
        # print('camera_data_depth',camera_data_depth.max())
        # max_tensor = torch.max(camera_data_depth,dim=(1,2))
        camera_data_depth = camera_data_depth/2.0

        # camera_data = torch.cat((camera_data_rgb,camera_data_depth),dim = -1)  # 图像数据

        left_link7_state  = self._robot.data.body_link_state_w[:,self.left_link7_idx,0:7].clone()   # 左臂第7关节值
        right_link7_state  = self._robot.data.body_link_state_w[:,self.right_link7_idx,0:7].clone()   # 右臂第7关节值

        left_link7_state[:,:3] = left_link7_state[:,:3] - self.robot_root_pos  # 改为相对位置
        right_link7_state[:,:3] = right_link7_state[:,:3] - self.robot_root_pos # 改为相对位置

        cur_left_joint_p  = self._robot.data.joint_pos[:,self.left_arm_indices].clone()   # 上一个关节位置
        cur_right_joint_p  = self._robot.data.joint_pos[:,self.right_arm_indices].clone()   # 上一个关节位置

        # print('真实角度差 ',self.robot_dof_targets[:,self.left_arm_indices + self.right_arm_indices] - self._robot.data.joint_pos[:,self.left_arm_indices+ self.right_arm_indices])

        cur_left_joint_v = (cur_left_joint_p - self.pre_left_joint_p)/self.physics_dt/self.cfg.decimation
        cur_right_joint_v = (cur_right_joint_p - self.pre_right_joint_p)/self.physics_dt/self.cfg.decimation

        # left_joint_ac = (cur_left_joint_v - self.pre_left_joint_v)/self.physics_dt/self.cfg.decimation
        # right_joint_ac = (cur_right_joint_v - self.pre_right_joint_v)/self.physics_dt/self.cfg.decimation


        self.pre_left_joint_p  = self._robot.data.joint_pos[:,self.left_arm_indices].clone()   # 上一个关节位置
        self.pre_right_joint_p  = self._robot.data.joint_pos[:,self.right_arm_indices].clone()   # 上一个关节位置

        # self.pre_left_joint_v  = cur_left_joint_v.clone()  # 上一个关节速度
        # self.pre_right_joint_v  = cur_right_joint_v.clone() # 上一个关节速度

        # dof_var = self._robot.data.joint_pos[:,self.left_arm_indices + self.right_arm_indices]   # 关节变量
        # print(dof_pos.shape)
        flag = self.flag.clone()
        # flag[flag==1] = 0
        # left_var = torch.concat((cur_left_joint_p.unsqueeze(2),cur_left_joint_v.unsqueeze(2)),dim=2)
        # right_var = torch.concat((cur_right_joint_p.unsqueeze(2),cur_right_joint_v.unsqueeze(2)),dim=2)
        # dof_var = torch.concat((left_var.reshape(-1,7*2),right_var.reshape(-1,7*2),left_link7_state, right_link7_state),dim=1) #flag.unsqueeze(1).repeat(1,7)
        dof_var = torch.concat((cur_left_joint_p,cur_right_joint_p,cur_left_joint_v, cur_right_joint_v, left_link7_state, right_link7_state),dim=1) 

        obs = torch.cat(
            (torch.reshape(camera_data_rgb,(-1,96*256*3)),
             torch.reshape(camera_data_depth,(-1,96*256*1)),
             dof_var
             ),
            dim=-1
        )

        observations = {"policy": obs.clone()}

        # if self.cfg.write_image_to_file:
        #     save_images_to_file(observations["policy"], f"cartpole_{data_type}.png")

        return observations



