# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Configuration for the QiruiItem"""

from __future__ import annotations

from scipy.spatial.transform import Rotation as R
import math
 
def euler2quaternion(euler):
    r = R.from_euler('xyz', euler, degrees=True)
    quaternion = r.as_quat()
    return [quaternion[3],quaternion[0],quaternion[1],quaternion[2]]
# print(euler2quaternion([67.084,180,90]))
import isaaclab.sim as sim_utils
from isaaclab.sensors import TiledCamera, TiledCameraCfg, save_images_to_file, ContactSensorCfg
from isaaclab.actuators import ImplicitActuatorCfg
from isaaclab.assets import ArticulationCfg,RigidObjectCfg,AssetBaseCfg
# from isaaclab.utils.assets import ISAAC_NUCLEUS_DIR

##
# Configuration
##
# 奇瑞机器人的配置参数
QiruiRobot_CFG = ArticulationCfg(
    prim_path="{ENV_REGEX_NS}/QiruiRobot",
    spawn=sim_utils.UsdFileCfg(
        # usd_path=f"USDs/QiruiRobot_upper.usd",
        usd_path=f"USDs/QiruiRobot.usd",
        activate_contact_sensors=True,
        rigid_props=sim_utils.RigidBodyPropertiesCfg(
            rigid_body_enabled=True,
            disable_gravity=True,
            retain_accelerations=False,
            enable_gyroscopic_forces=False,
            # angular_damping=0.01,
            # max_linear_velocity=10.0,
            # max_angular_velocity=64 / math.pi * 180.0,
            max_depenetration_velocity=10.0,
            max_contact_impulse=1e32,
        ),
        articulation_props=sim_utils.ArticulationRootPropertiesCfg(
            enabled_self_collisions=True,
            solver_position_iteration_count=8,
            solver_velocity_iteration_count=0,
            sleep_threshold=0.005,
            stabilization_threshold=0.0005,
        ),
       # collision_props=sim_utils.CollisionPropertiesCfg(contact_offset=0.005, rest_offset=0.0),
    ),

    init_state=ArticulationCfg.InitialStateCfg(
        pos=(0, 0.75, 1.00),
        rot = [float(0.7071), float(0.0), float(0.0), float(-0.7071)],
        # rot= float(euler2quaternion([0.0,0.0,-90.0])),
        joint_pos = {
                ".*_(left|right)_arm_joint1":0.6,
                ".*_left_arm_joint2":0.44,
                ".*_right_arm_joint2":-0.44,
                ".*_(left|right)_arm_joint3":0.41,
                ".*_left_arm_joint4":-1.07,
                ".*_right_arm_joint4":1.07,
                ".*_(left|right)_arm_joint5":0.40,
                ".*_(left|right)_arm_joint6":0.0,
                ".*_(left|right)_arm_joint7":0.0,
                "(left|right)_hand_thumb_proximal_yaw_joint":1.3
            },
    ),

    actuators = {
        "Arms": ImplicitActuatorCfg(
            joint_names_expr=['.*_(left|right)_arm_joint[1-7]'],
            effort_limit={
                ".*_(left|right)_arm_joint[1-2]":100000.0,
                ".*_(left|right)_arm_joint[3-4]":100000.0,
                ".*_(left|right)_arm_joint[5-7]":100000.0,
            },
            velocity_limit=100.0,
            stiffness={
                ".*_(left|right)_arm_joint[1-4]":10000000.0,
                ".*_(left|right)_arm_joint[5-7]":10000000.0,
            },
            damping={
                ".*_(left|right)_arm_joint[1-4]":100.0,
                ".*_(left|right)_arm_joint[5-7]":100.0,
            },
            friction=0.1,
        ),
        "Hands": ImplicitActuatorCfg(
            joint_names_expr=['(left|right)_hand_(index|middle|pinky|ring)_(proximal|intermediate)_joint', 
                            '(left|right)_hand_thumb_(proximal_(pitch|yaw)|intermediate|distal)_joint'],
            effort_limit=3.0,
            velocity_limit=20.0,
            stiffness={
                '(left|right)_hand_(index|middle|pinky|ring)_(proximal|intermediate)_joint':50.0,
                '(left|right)_hand_thumb_(proximal_pitch|intermediate|distal)_joint':30.0,
                '(left|right)_hand_thumb_proximal_yaw_joint':30.0,
            },
            damping={
                '(left|right)_hand_(index|middle|pinky|ring)_(proximal|intermediate)_joint':20,
                '(left|right)_hand_thumb_(proximal_pitch|intermediate|distal)_joint':20,
                '(left|right)_hand_thumb_proximal_yaw_joint':20,
            },
            friction=1.3,
        ),
        # "Legs": ImplicitActuatorCfg(
        #     joint_names_expr=['.*_(left|right)_hip_.*', '.*_(left|right)_toe_.*', '.*_(left|right)_tarsus'],
        #     effort_limit=0.1,
        #     velocity_limit=0.1,
        #     stiffness=1e2,
        #     damping=1e6,
        #     friction=1e2,
        # ),
    },

    soft_joint_pos_limit_factor=1.0,
)

# 桌子的配置参数
Table_CFG = RigidObjectCfg(
        prim_path="{ENV_REGEX_NS}/Table",
        spawn=sim_utils.UsdFileCfg(
            activate_contact_sensors=True,
            usd_path=f"USDs/table.usd",
            # rigid_props=sim_utils.RigidBodyPropertiesCfg(disable_gravity=True,),
            mass_props=sim_utils.MassPropertiesCfg(mass=10000.0),
            collision_props=sim_utils.CollisionPropertiesCfg(),
        ),
        init_state=RigidObjectCfg.InitialStateCfg(pos=[0.0, 0.5, 0.55], rot=[1.0, 0.0, 0.0, 0.0]),
    )

# 水壶的配置参数
Kettle_CFG = RigidObjectCfg(
        prim_path="{ENV_REGEX_NS}/Kettle",
        spawn=sim_utils.UsdFileCfg(
            activate_contact_sensors=True,
            usd_path=f"USDs/bottle.usd",
            rigid_props=sim_utils.RigidBodyPropertiesCfg(),
            mass_props=sim_utils.MassPropertiesCfg(mass=1.0),
            collision_props=sim_utils.CollisionPropertiesCfg(),
        ),
        init_state=RigidObjectCfg.InitialStateCfg(pos=[-0.16, 0.27, 1.11], rot=[0.7071, 0.7071, 0.0, 0.0]),
)

# 杯子的配置参数
Cup_CFG = RigidObjectCfg(
        prim_path="{ENV_REGEX_NS}/Cup",
        spawn=sim_utils.UsdFileCfg(
            activate_contact_sensors=True,
            usd_path=f"USDs/cup4.usd",
            rigid_props=sim_utils.RigidBodyPropertiesCfg(),
            mass_props=sim_utils.MassPropertiesCfg(mass=0.5),
            collision_props=sim_utils.CollisionPropertiesCfg(),
        ),
        init_state=RigidObjectCfg.InitialStateCfg(pos=[0.16, 0.27, 1.13], rot=[0, 0, 0.7071, -0.7071]),
)


# sensors
# 相机的配置参数
Camera_CFG = TiledCameraCfg(
    prim_path="{ENV_REGEX_NS}/QiruiRobot/front_cam",
    debug_vis = True,
    update_period=0.1,
    height=180,
    width=320,
    data_types=["rgb", "depth"],
    spawn=sim_utils.PinholeCameraCfg(
        focal_length=24.0, focus_distance=400.0, horizontal_aperture=30, clipping_range=(0.1, 1.0e5)
    ),
    offset=TiledCameraCfg.OffsetCfg(pos=(0.06, 0.0, 0.3527), rot=[-0.40558, 0.57923, -0.57923, 0.40558], convention="ros"),
)

Camera_CFG2 = TiledCameraCfg(
    prim_path="{ENV_REGEX_NS}/QiruiRobot/head_cam",
    debug_vis = True,
    update_period=0.1,
    height=360,
    width=640,
    data_types=["rgb", "depth"],
    spawn=sim_utils.PinholeCameraCfg(
        focal_length=24.0, focus_distance=400.0, horizontal_aperture=30, clipping_range=(0.1, 1.0e5)
    ),
    offset=TiledCameraCfg.OffsetCfg(pos=(0.57, -0.0, 1.0), rot=[0 , -0.7071068, 0.7071068, 0], convention="ros"),
)


# 接触传感器的配置参数（可不管，实际没有）
contact_forces_left_hand_intermediate_cfg = ContactSensorCfg(
    prim_path="{ENV_REGEX_NS}/QiruiRobot/left_hand_.*_intermediate", update_period=0.0, history_length=6, debug_vis=True
)

contact_forces_right_hand_intermediate_cfg = ContactSensorCfg(
    prim_path="{ENV_REGEX_NS}/QiruiRobot/right_hand_.*_intermediate", update_period=0.0, history_length=6, debug_vis=True
)
