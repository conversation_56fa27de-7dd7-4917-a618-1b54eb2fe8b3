# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Configuration for the QiruiItem"""

from __future__ import annotations

from scipy.spatial.transform import Rotation as R
import math
 
def euler2quaternion(euler):
    r = R.from_euler('xyz', euler, degrees=True)
    quaternion = r.as_quat()
    return [quaternion[3],quaternion[0],quaternion[1],quaternion[2]]
# print(euler2quaternion([67.084,180,90]))
import isaaclab.sim as sim_utils
from isaaclab.sensors import TiledCamera, TiledCameraCfg, save_images_to_file, ContactSensorCfg
from isaaclab.actuators import ImplicitActuatorCfg
from isaaclab.assets import ArticulationCfg,RigidObjectCfg,AssetBaseCfg
##
# Configuration
##
# 启江机器人的配置参数
QiJiangRobot_CFG = ArticulationCfg(
    prim_path="{ENV_REGEX_NS}/QiJiangRobot",
    spawn=sim_utils.UsdFileCfg(
        usd_path=f"/home/<USER>/QiruiItem/IsaacLabExtensionTemplate/exts/Qiruiitem/Qiruiitem/tasks/QiJiangRobot4.usd",
        activate_contact_sensors=True,
        rigid_props=sim_utils.RigidBodyPropertiesCfg(
            rigid_body_enabled=True,
            disable_gravity=True,
            retain_accelerations=False,
            enable_gyroscopic_forces=False,
            # angular_damping=0.01,
            # max_linear_velocity=10.0,
            # max_angular_velocity=64 / math.pi * 180.0,
            max_depenetration_velocity=10000.0,
            max_contact_impulse=1e32,
        ),
        articulation_props=sim_utils.ArticulationRootPropertiesCfg(
            enabled_self_collisions=False,
            solver_position_iteration_count=8,
            solver_velocity_iteration_count=0,
            sleep_threshold=0.005,
            stabilization_threshold=0.0005,
        ),
       # collision_props=sim_utils.CollisionPropertiesCfg(contact_offset=0.005, rest_offset=0.0),
    ),

    init_state=ArticulationCfg.InitialStateCfg(
        pos=(0.0, 0.6, 0.75),
        rot = [float(0.7071), float(0.0), float(0.0), float(-0.7071)],
        # rot= float(euler2quaternion([0.0,0.0,-90.0])),
        joint_pos = {"(l|r)_arm_Joint1":1.2,
                     "(l|r)_arm_Joint2":-0.8,
                     "(l|r)_arm_Joint3":-1.5,
                     "(l|r)_arm_Joint4":0.9,
                     "(l|r)_arm_Joint5":1.2,
                     "(l|r)_arm_Joint6":0,
                     "(l|r)_arm_Joint7":0}
        # joint_pos = {"(l|r)_arm_Joint1":0.0,
        #              "(l|r)_arm_Joint2":-0.0,
        #              "(l|r)_arm_Joint3":-0.0,
        #              "(l|r)_arm_Joint4":1.57,
        #              "(l|r)_arm_Joint5":0.0,
        #              "(l|r)_arm_Joint6":0,
        #              "(l|r)_arm_Joint7":0}
        # joint_pos = {"(l|r)_arm_Joint*":0}
    ),

    actuators = {
        "Arms": ImplicitActuatorCfg(
            joint_names_expr=['(l|r)_arm_Joint[1-7]'],
            effort_limit={
                "(l|r)_arm_Joint[1-7]":100.0
            },
            velocity_limit=100.0,
            stiffness={
                "(l|r)_arm_Joint[1-7]":200.0
            },
            damping={
                "(l|r)_arm_Joint[1-7]":10.0
            },
            friction=0.3,
        ),
        "Hands": ImplicitActuatorCfg(
            joint_names_expr=['(l|r)_hand_Joint[1-6]'],
            effort_limit=100.0,
            velocity_limit=100,
            stiffness={
                '(l|r)_hand_Joint[1-6]':200.0
            },
            damping={
                '(l|r)_hand_Joint[1-6]':50.0
            },
            friction=1.3,
        )
    },

    soft_joint_pos_limit_factor=1.0,
)

# 桌子的配置参数
Table_CFG = RigidObjectCfg(
        prim_path="{ENV_REGEX_NS}/Table",
        spawn=sim_utils.UsdFileCfg(
            activate_contact_sensors=True,
            usd_path=f"/home/<USER>/QiruiItem/IsaacLabExtensionTemplate/exts/Qiruiitem/Qiruiitem/tasks/table.usd",
            mass_props=sim_utils.MassPropertiesCfg(mass=10.0),
            collision_props=sim_utils.CollisionPropertiesCfg(),
        ),
        init_state=RigidObjectCfg.InitialStateCfg(pos=[0.0, 0.5, 0.55], rot=[1.0, 0.0, 0.0, 0.0]),

    )


# 水壶的配置参数
Kettle_CFG = RigidObjectCfg(
        prim_path="{ENV_REGEX_NS}/Kettle",
        spawn=sim_utils.UsdFileCfg(
            activate_contact_sensors=True,
            usd_path=f"/home/<USER>/QiruiItem/IsaacLabExtensionTemplate/exts/Qiruiitem/Qiruiitem/tasks/bottle.usd",
            rigid_props=sim_utils.RigidBodyPropertiesCfg(),
            mass_props=sim_utils.MassPropertiesCfg(mass=0.3),
            collision_props=sim_utils.CollisionPropertiesCfg(),
        ),
        init_state=RigidObjectCfg.InitialStateCfg(pos=[-0.25,0.2, 1.12], rot=[0.7071, 0.7071, 0.0, 0.0]),
)

# 杯子的配置参数
Cup_CFG = RigidObjectCfg(
        prim_path="{ENV_REGEX_NS}/Cup",
        spawn=sim_utils.UsdFileCfg(
            activate_contact_sensors=True,
            usd_path=f"/home/<USER>/QiruiItem/IsaacLabExtensionTemplate/exts/Qiruiitem/Qiruiitem/tasks/cup3.usd",
            rigid_props=sim_utils.RigidBodyPropertiesCfg(),
            mass_props=sim_utils.MassPropertiesCfg(mass=0.1),
            collision_props=sim_utils.CollisionPropertiesCfg(),
        ),
        init_state=RigidObjectCfg.InitialStateCfg(pos=[0.15, 0.2, 1.12], rot=[0, 0, 0.7071, -0.7071]),
)

# sensors
# 相机的配置参数
Camera_CFG = TiledCameraCfg(
    prim_path="{ENV_REGEX_NS}/QiJiangRobot/front_cam",
    debug_vis = True,
    update_period=0.1,
    height=480,
    width=640,
    data_types=["rgb", "distance_to_image_plane"],
    spawn=sim_utils.PinholeCameraCfg(
        focal_length=24.0, focus_distance=400.0, horizontal_aperture=60, clipping_range=(0.1, 1.0e5)
    ),
    # offset=TiledCameraCfg.OffsetCfg(pos=(0.2, 0.0, 0.7), rot=[0.1624918, -0.6881834, 0.6881834, -0.1624918], convention="ros"),
    offset=TiledCameraCfg.OffsetCfg(pos=(0.2, 0.0, 0.8), rot=[0.1624918, -0.6881834, 0.6881834, -0.1624918], convention="ros"),
)

# Camera_CFG = CameraCfg(
#     prim_path="{ENV_REGEX_NS}/QiJiangRobot/front_cam",
#     update_period=0.1,
#     height=480,
#     width=640,
#     data_types=["rgb", "distance_to_image_plane"],
#     spawn=sim_utils.PinholeCameraCfg(
#         focal_length=24.0, focus_distance=400.0, horizontal_aperture=20.955, clipping_range=(0.1, 1.0e5)
#     ),
#     offset=CameraCfg.OffsetCfg(pos=(0.2, 0.0, 0.7), rot=(0.1624918, -0.6881834, 0.6881834, -0.1624918), convention="ros"),
# )

# 接触传感器的配置参数（可不管，实际没有）
contact_forces_left_hand_intermediate_cfg = ContactSensorCfg(
    prim_path="{ENV_REGEX_NS}/QiJiangRobot/left_hand_.*_intermediate", update_period=0.0, history_length=6, debug_vis=True
)

contact_forces_right_hand_intermediate_cfg = ContactSensorCfg(
    prim_path="{ENV_REGEX_NS}/QiJiangRobot/right_hand_.*_intermediate", update_period=0.0, history_length=6, debug_vis=True
)
