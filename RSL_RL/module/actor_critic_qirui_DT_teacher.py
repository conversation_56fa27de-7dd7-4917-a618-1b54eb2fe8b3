#  Copyright 2021 ETH Zurich, NVIDIA CORPORATION
#  SPDX-License-Identifier: BSD-3-Clause


from __future__ import annotations

import torch
import torch.nn as nn
from torch.distributions import Normal
import torchvision.transforms as transforms
from torchvision.models import resnet18
import torch.nn.functional as F
import timm


class PatchEmbed(nn.Module):
    def __init__(self, patch_size=4):
        super().__init__()
        self.patch_size = patch_size
        self.unfold = nn.Unfold(kernel_size=patch_size, stride=patch_size)

    def forward(self, x):
        B, C, H, W = x.shape
        patches = self.unfold(x)  # [B, C * patch_size^2, N]
        patches = patches.transpose(1, 2)  # [B, N, C * patch_size^2]

        # 生成位置信息 (行坐标，列坐标)
        num_patches_h = H // self.patch_size
        num_patches_w = W // self.patch_size
        coords = torch.stack(torch.meshgrid(
            torch.arange(num_patches_h),
            torch.arange(num_patches_w),
            indexing='ij'
        ), dim=-1).reshape(-1, 2).float().to(x.device)  # [N, 2]

        coords = coords.unsqueeze(0).repeat(B, 1, 1)  # [B, N, 2]
        patches = torch.cat([patches, coords], dim=-1)  # 拼接位置信息
        return patches, (H, W)

class ConvBNReLU(nn.Module):
    def __init__(self, in_channels, out_channels, kernel_size, stride, padding):
        super().__init__()
        self.conv = nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding, bias=False)
        self.bn = nn.BatchNorm2d(out_channels)
        self.relu = nn.ELU(inplace=True)

    def forward(self, x):
        return self.relu(self.bn(self.conv(x)))

class MobileViTBlock(nn.Module):
    def __init__(self, in_channels, ffn_dim, num_heads, depth, H, W , patch_size=4, reduce_dim=16):
        super().__init__()
        self.patch_size = patch_size
        self.reduce_conv = ConvBNReLU(in_channels, reduce_dim, kernel_size=1, stride=1, padding=0)
        self.unfold = nn.Unfold(kernel_size=patch_size, stride=patch_size)
        self.dim = reduce_dim * (patch_size ** 2)
        self.layers = nn.Sequential(
            *[nn.TransformerEncoderLayer(d_model=self.dim, nhead=num_heads, dim_feedforward=ffn_dim, batch_first=True) 
              for _ in range(depth)]
        )
        self.expand_conv = ConvBNReLU(reduce_dim, in_channels, kernel_size=1, stride=1, padding=0)
        fold_output_size = (H, W)
        self.fold = nn.Fold(output_size=fold_output_size, kernel_size=self.patch_size, stride=self.patch_size) # 动态创建 fold
    
    def forward(self, x):
        x = self.reduce_conv(x)
        x = self.unfold(x)
        N = x.shape[-1]
        x = x.permute(0, 2, 1)
        x = self.layers(x)
        x = x.permute(0, 2, 1)
        
        x = self.fold(x)
        x = self.expand_conv(x)
        return x

class MobileViT(nn.Module):
    def __init__(self, input_channels=1, output_dim=256):
        super().__init__()
        self.conv1 = ConvBNReLU(input_channels, 16, 3, 2, 1)
        self.conv2 = ConvBNReLU(16, 16, 3, 2, 1)
        self.conv3 = ConvBNReLU(16, 16, 3, 2, 1)  # 进一步减少特征尺寸
        h, w = 28,28
        self.mvit_block = MobileViTBlock(in_channels=16, ffn_dim=128, num_heads=4, H = h, W = w, depth=2, patch_size=4, reduce_dim=16)
        
        self.conv4 = ConvBNReLU(16, 32, 3, 2, 1)
        self.pool = nn.AdaptiveAvgPool2d(7)  # 将特征图池化到 7x7
        self.fc = nn.Linear(7*7*32, output_dim)
    
    def forward(self, x):
        x = self.conv1(x)
        x = self.conv2(x)
        x = self.conv3(x)
        x = self.mvit_block(x)
        x = self.conv4(x)
        x = self.pool(x).view(x.size(0), -1)
        x = self.fc(x)
        return x


class SplitAndProcessNetwork(nn.Module):
    def __init__(self):
        super(SplitAndProcessNetwork, self).__init__()

        # 子网络11：处理输入a的卷积网络
        self.conv_net = nn.Sequential(
            # 卷积一层
            MobileViT(4,256),
            nn.ELU(inplace=True),
            # 关节角度的全连接网络部分       
        )

        # 子网络22：处理输入a2的卷积网络
        self.conv_net2 = nn.Sequential(
            # 卷积一层
            MobileViT(4,256),
            nn.ELU(inplace=True),
            # 关节角度的全连接网络部分       
        )
                # 加载预训练模型
        self.fc_conv = nn.Sequential(
            nn.Linear(512, 256),
            nn.ELU(inplace=True),
            nn.Linear(256, 256),
            nn.ELU(inplace=True)
        )

        # 子网络2：处理输入b的MLP
        self.fc_joint = nn.Sequential(
            nn.Linear(21, 256),
            nn.ELU(inplace=True),
            nn.Linear(256, 256),
            nn.ELU(inplace=True)
        )
        
        # 合并后的全连接部分
        self.fc_concat = nn.Sequential(
            nn.Linear(256 + 256, 256),
            nn.ELU(inplace=True),
            nn.Linear(256, 256),
            nn.ELU(inplace=True)
        )

    def forward(self, x):
        # x: (batch_size, 256*256*4 + 14)
        
        # 将x拆分为a和b
        a = x[:, :224*224*4]  # 前部分是 a

        a2 = x[:, 224*224*4:224*224*4*2]  # 中间部分是 a2前部分是 a

        b = x[:, 224*224*4*2:]  # 后部分是 b
        
        # 处理输入a
        # image_tensor = F.interpolate(a.view(-1,256,256,3).permute(0,3,1,2), size=(224, 224), mode='bilinear', align_corners=False)  # 调整大小
        # a_features = self.model(image_tensor)

        # 处理输入a
        a = a.view(-1, 4, 224, 224)  # Reshape为图像大小 (batch_size, 3, 24, 24)
        # resize_transform = transforms.Resize((224, 224)) 
        # a = resize_transform(a)
        a_features1 = self.conv_net(a)

        a2 = a2.view(-1, 4, 224, 224)  # Reshape为图像大小 (batch_size, 3, 24, 24)
        # resize_transform = transforms.Resize((224, 224)) 
        # a = resize_transform(a)
        a_features2 = self.conv_net2(a2) 

        a_features = torch.cat((a_features1, a_features2), dim=1)
        a_features = self.fc_conv(a_features)

        # 处理输入b
        b_features = self.fc_joint(b)
        
        # 拼接a和b的特征
        combined_features = torch.cat((a_features, b_features), dim=1)
        
        # 最终的MLP
        output = self.fc_concat(combined_features)
        return output
    
# 将网络封装为 nn.Sequential 格式
class SequentialWrapper(nn.Module):
    def __init__(self):
        super(SequentialWrapper, self).__init__()
        self.network = SplitAndProcessNetwork()
        self.in_features = 224*224*8 + 21
    
    def forward(self, inputs):
        return self.network(inputs)


class ActorCritic_QIRUI_DT_teacher(nn.Module):
    is_recurrent = False

    def __init__(
        self,
        num_actor_obs,
        num_critic_obs,
        num_actions,
        actor_hidden_dims=[256, 256, 256],
        critic_hidden_dims=[256, 256, 256],
        activation="elu",
        init_noise_std=1.0,
        **kwargs,
    ):
        if kwargs:
            print(
                "ActorCritic.__init__ got unexpected arguments, which will be ignored: "
                + str([key for key in kwargs.keys()])
            )
        super().__init__()
        activation = get_activation(activation)

        mlp_input_dim_a = num_actor_obs
        mlp_input_dim_c = num_critic_obs

        # Policy



        actor_layers = []
        actor_layers.append(SequentialWrapper())
        actor_layers.append(nn.Linear(256, num_actions))
        actor_layers.append(nn.Tanh())

        # actor_layers.append(nn.Linear(mlp_input_dim_a, actor_hidden_dims[0]))
        # actor_layers.append(activation)
        # for layer_index in range(len(actor_hidden_dims)):
        #     if layer_index == len(actor_hidden_dims) - 1:
        #         actor_layers.append(nn.Linear(actor_hidden_dims[layer_index], num_actions))
        #     else:
        #         actor_layers.append(nn.Linear(actor_hidden_dims[layer_index], actor_hidden_dims[layer_index + 1]))
        #         actor_layers.append(activation)
        self.actor = nn.Sequential(*actor_layers)

        # Value function
        critic_layers = []
        critic_layers.append(SequentialWrapper())
        critic_layers.append(nn.Linear(256, 1))
        # critic_layers.append(nn.Linear(mlp_input_dim_c, critic_hidden_dims[0]))
        # critic_layers.append(activation)
        # for layer_index in range(len(critic_hidden_dims)):
        #     if layer_index == len(critic_hidden_dims) - 1:
        #         critic_layers.append(nn.Linear(critic_hidden_dims[layer_index], 1))
        #     else:
        #         critic_layers.append(nn.Linear(critic_hidden_dims[layer_index], critic_hidden_dims[layer_index + 1]))
        #         critic_layers.append(activation)
        self.critic = nn.Sequential(*critic_layers)

        print(f"Actor network: {self.actor}")
        print(f"Critic network: {self.critic}")

        # Action noise
        self.std = nn.Parameter(init_noise_std * torch.ones(num_actions))
        self.distribution = None
        # disable args validation for speedup
        Normal.set_default_validate_args = False

        # seems that we get better performance without init
        # self.init_memory_weights(self.memory_a, 0.001, 0.)
        # self.init_memory_weights(self.memory_c, 0.001, 0.)

    @staticmethod
    # not used at the moment
    def init_weights(sequential, scales):
        [
            torch.nn.init.orthogonal_(module.weight, gain=scales[idx])
            for idx, module in enumerate(mod for mod in sequential if isinstance(mod, nn.Linear))
        ]

    def reset(self, dones=None):
        pass

    def forward(self):
        raise NotImplementedError

    @property
    def action_mean(self):
        return self.distribution.mean

    @property
    def action_std(self):
        return self.distribution.stddev

    @property
    def entropy(self):
        return self.distribution.entropy().sum(dim=-1)

    def update_distribution(self, observations):
        mean = self.actor(observations)
        self.distribution = Normal(mean, mean * 0.0 + self.std)

    def act(self, observations, **kwargs):
        self.update_distribution(observations)
        return self.distribution.sample()

    def get_actions_log_prob(self, actions):
        return self.distribution.log_prob(actions).sum(dim=-1)

    def act_inference(self, observations):
        actions_mean = self.actor(observations)
        return actions_mean

    def evaluate(self, critic_observations, **kwargs):
        value = self.critic(critic_observations)
        return value


def get_activation(act_name):
    if act_name == "elu":
        return nn.ELU()
    elif act_name == "selu":
        return nn.SELU()
    elif act_name == "relu":
        return nn.ReLU()
    elif act_name == "crelu":
        return nn.CReLU()
    elif act_name == "lrelu":
        return nn.LeakyReLU()
    elif act_name == "tanh":
        return nn.Tanh()
    elif act_name == "sigmoid":
        return nn.Sigmoid()
    else:
        print("invalid activation function!")
        return None
