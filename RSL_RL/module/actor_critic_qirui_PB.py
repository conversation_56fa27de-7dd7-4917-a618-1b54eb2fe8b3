#  Copyright 2021 ETH Zurich, NVIDIA CORPORATION
#  SPDX-License-Identifier: BSD-3-Clause


from __future__ import annotations

import torch
import torch.nn as nn
from torch.distributions import Normal
import torchvision.transforms as transforms
from torchvision.models import resnet18
import torch.nn.functional as F
import timm

class MLP(nn.Module):
    def __init__(self, input_dim=6, hidden_dim=256, output_dim=256, num_layers=5):
        super(MLP, self).__init__()
        self.input_fc = nn.Linear(input_dim, hidden_dim)
        self.elu = nn.ELU()
        self.blocks = nn.ModuleList([nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim),
                nn.ELU(),
                nn.Dropout(0.3)
        ) for _ in range(num_layers)]
        )
        # self.droupout = nn.Dropout(0.3)

        self.output_fc = nn.Linear(hidden_dim, output_dim)
        

    def forward(self, x):
        x = self.input_fc(x)
        x = self.elu(x)
        for block in self.blocks:
            x = block(x) + x # 
        # self.droupout(x)
        return self.output_fc(x)

class ResidualBlock(nn.Module):
    """1D CNN 残差块"""
    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1, padding=1):
        super(ResidualBlock, self).__init__()
        self.conv1 = nn.Conv1d(in_channels, out_channels, kernel_size, stride, padding)
        self.bn1 = nn.BatchNorm1d(out_channels)
        self.conv2 = nn.Conv1d(out_channels, out_channels, kernel_size, stride, padding)
        self.bn2 = nn.BatchNorm1d(out_channels)
        self.relu = nn.ReLU()

        # 如果输入通道数不匹配，则用 1x1 卷积调整
        self.shortcut = nn.Conv1d(in_channels, out_channels, kernel_size=1) if in_channels != out_channels else nn.Identity()

    def forward(self, x):
        res = self.shortcut(x)  # Shortcut 连接
        out = self.relu(self.bn1(self.conv1(x)))
        out = self.bn2(self.conv2(out))
        out += res  # 残差连接
        return self.relu(out)

class Conv1DModel(nn.Module):
    """深层 ResNet 风格的 1D CNN"""
    def __init__(self, in_channels=1, num_residual_blocks=6, hidden_dim=256, embedding_dim=256):
        super(Conv1DModel, self).__init__()
        
        # 初始卷积层
        self.initial_conv = nn.Conv1d(in_channels, hidden_dim, kernel_size=7, padding=3)
        self.initial_bn = nn.BatchNorm1d(hidden_dim)
        self.initial_relu = nn.ReLU()

        # 堆叠多个残差块
        self.residual_blocks = nn.Sequential(
            *[ResidualBlock(hidden_dim, hidden_dim) for _ in range(num_residual_blocks)]
        )

        self.pool = nn.AdaptiveAvgPool1d(1)

        # SE模块
        self.se = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 4),
            nn.ReLU(),
            nn.Linear(hidden_dim // 4, hidden_dim),
            nn.Sigmoid()
        )

        # Global Average Pooling + FC
        # self.gap = nn.AdaptiveAvgPool1d(7)  # GAP 降维
        self.fc = nn.Linear(hidden_dim, embedding_dim)  # 64 → 256

    def forward(self, x):
        x = x.unsqueeze(1)  # 增加通道维度
        x = self.initial_relu(self.initial_bn(self.initial_conv(x)))  # 初始卷积
        
        x = self.residual_blocks(x)  # 多层残差块
        # print("x.shape",x.shape)
        # SE 注意力
        x = self.pool(x).squeeze(-1)  # GAP 降维
        # print("x.shape",x.shape)
        se_weight = self.se(x)
        x = x * se_weight
        
        # x = self.gap(x).squeeze(-1)  # GAP 降维
        x = self.fc(x)  # 全连接层转换到 256 维
        return x

class Nets(nn.Module):
    """深层 ResNet 风格的 1D CNN"""  # object_up_dim:64 → 21
    def __init__(self, object_dim=6, joint_dim=21):
        super(Nets, self).__init__()

        self.process_object = MLP(input_dim=object_dim, hidden_dim=64,output_dim=64,num_layers=2)
        self.process_joint = MLP(input_dim=joint_dim, hidden_dim=64,output_dim=64,num_layers=2)     
        self.last_process = MLP(input_dim=128, hidden_dim=256,output_dim=256,num_layers=4)

        # Global Average Pooling + FC
        # self.gap = nn.AdaptiveAvgPool1d(7)  # GAP 降维

    def forward(self, x):
        x1 = x[:,:6]
        x2 = x[:,6:]
        x1_p= self.process_object(x1)
        x2_p = self.process_joint(x2) 
        # x2_con = x2 
        x_last = torch.cat((x1_p, x2_p), dim=1)
        # x = self.gap(x).squeeze(-1)  # GAP 降维
        x = self.last_process(x_last)  # 全连接层转换到 256 维
        return x
     
# 将网络封装为 nn.Sequential 格式
class SequentialWrapper(nn.Module):
    def __init__(self):
        super(SequentialWrapper, self).__init__()
        self.network = Nets()
        self.in_features = 27
    
    def forward(self, inputs):
        return self.network(inputs)


class ActorCritic_QIRUI_PB(nn.Module):
    is_recurrent = False

    def __init__(
        self,
        num_actor_obs,
        num_critic_obs,
        num_actions,
        actor_hidden_dims=[256, 256, 256],
        critic_hidden_dims=[256, 256, 256],
        activation="elu",
        init_noise_std=1.0,
        **kwargs,
    ):
        if kwargs:
            print(
                "ActorCritic.__init__ got unexpected arguments, which will be ignored: "
                + str([key for key in kwargs.keys()])
            )
        super().__init__()
        activation = get_activation(activation)

        mlp_input_dim_a = num_actor_obs
        mlp_input_dim_c = num_critic_obs

        # Policy



        actor_layers = []
        actor_layers.append(SequentialWrapper())
        actor_layers.append(nn.Linear(256, num_actions))
        actor_layers.append(nn.Tanh())

        # actor_layers.append(nn.Linear(mlp_input_dim_a, actor_hidden_dims[0]))
        # actor_layers.append(activation)
        # for layer_index in range(len(actor_hidden_dims)):
        #     if layer_index == len(actor_hidden_dims) - 1:
        #         actor_layers.append(nn.Linear(actor_hidden_dims[layer_index], num_actions))
        #     else:
        #         actor_layers.append(nn.Linear(actor_hidden_dims[layer_index], actor_hidden_dims[layer_index + 1]))
        #         actor_layers.append(activation)
        self.actor = nn.Sequential(*actor_layers)

        # Value function
        critic_layers = []
        critic_layers.append(SequentialWrapper())
        critic_layers.append(nn.Linear(256, 1))
        # critic_layers.append(nn.Linear(mlp_input_dim_c, critic_hidden_dims[0]))
        # critic_layers.append(activation)
        # for layer_index in range(len(critic_hidden_dims)):
        #     if layer_index == len(critic_hidden_dims) - 1:
        #         critic_layers.append(nn.Linear(critic_hidden_dims[layer_index], 1))
        #     else:
        #         critic_layers.append(nn.Linear(critic_hidden_dims[layer_index], critic_hidden_dims[layer_index + 1]))
        #         critic_layers.append(activation)
        self.critic = nn.Sequential(*critic_layers)

        print(f"Actor network: {self.actor}")
        print(f"Critic network: {self.critic}")

        # Action noise
        self.std = nn.Parameter(init_noise_std * torch.ones(num_actions))
        self.distribution = None
        # disable args validation for speedup
        Normal.set_default_validate_args = False

        # seems that we get better performance without init
        # self.init_memory_weights(self.memory_a, 0.001, 0.)
        # self.init_memory_weights(self.memory_c, 0.001, 0.)

    @staticmethod
    # not used at the moment
    def init_weights(sequential, scales):
        [
            torch.nn.init.orthogonal_(module.weight, gain=scales[idx])
            for idx, module in enumerate(mod for mod in sequential if isinstance(mod, nn.Linear))
        ]

    def reset(self, dones=None):
        pass

    def forward(self):
        raise NotImplementedError

    @property
    def action_mean(self):
        return self.distribution.mean

    @property
    def action_std(self):
        return self.distribution.stddev

    @property
    def entropy(self):
        return self.distribution.entropy().sum(dim=-1)

    def update_distribution(self, observations):
        mean = self.actor(observations)
        self.distribution = Normal(mean, mean * 0.0 + self.std)

    def act(self, observations, **kwargs):
        self.update_distribution(observations)
        return self.distribution.sample()

    def get_actions_log_prob(self, actions):
        return self.distribution.log_prob(actions).sum(dim=-1)

    def act_inference(self, observations):
        actions_mean = self.actor(observations)
        return actions_mean

    def evaluate(self, critic_observations, **kwargs):
        value = self.critic(critic_observations)
        return value


def get_activation(act_name):
    if act_name == "elu":
        return nn.ELU()
    elif act_name == "selu":
        return nn.SELU()
    elif act_name == "relu":
        return nn.ReLU()
    elif act_name == "crelu":
        return nn.CReLU()
    elif act_name == "lrelu":
        return nn.LeakyReLU()
    elif act_name == "tanh":
        return nn.Tanh()
    elif act_name == "sigmoid":
        return nn.Sigmoid()
    else:
        print("invalid activation function!")
        return None
