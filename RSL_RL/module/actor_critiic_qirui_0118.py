#  Copyright 2021 ETH Zurich, NVIDIA CORPORATION
#  SPDX-License-Identifier: BSD-3-Clause


from __future__ import annotations

import torch
import torch.nn as nn
from torch.distributions import Normal


class SplitAndProcessNetwork(nn.Module):
    def __init__(self):
        super(SplitAndProcessNetwork, self).__init__()
        
        # 子网络1：处理输入a的卷积网络
        self.conv_net = nn.Sequential(
            # 卷积一层
            nn.Conv2d(4, 16, kernel_size=3, stride=2, padding=1),  # 256x256x4 -> 128x128x16
            nn.MaxPool2d(kernel_size=2),  # 128x128x16 -> 64x64x16
            nn.ReLU(),
            # 卷积二层
            nn.Conv2d(16, 32, kernel_size=3, stride=2, padding=1),  # 64x64x16 -> 32x32x32
            nn.MaxPool2d(kernel_size=2),  # 32x32x32 -> 16x16x32
            nn.ReLU(),
            # 卷积三层
            nn.Conv2d(32, 64, kernel_size=3, stride=2, padding=1),  # 16x16x64 -> 8x8x64
            nn.MaxPool2d(kernel_size=2),  # 8x8x64 -> 4x4x64
            nn.ReLU(),
            # 卷积四层
            nn.Conv2d(64, 128, kernel_size=3, stride=2, padding=1),  # 4x4x64 -> 2x2x128
            nn.ReLU(),
            nn.Flatten()  # 展平，输出 (2*2*128=512)
            # 关节角度的全连接网络部分       
        )
        
        # 子网络2：处理输入b的MLP
        self.fc_joint = nn.Sequential(
            nn.Linear(14 + 24, 256),
            nn.ReLU(),
            nn.Linear(256, 256),
            nn.ReLU()
        )
        
        # 合并后的全连接部分
        self.fc_concat = nn.Sequential(
            nn.Linear(512 + 256, 256),
            nn.ReLU(),
            nn.Linear(256, 256),
            nn.ReLU()
        )

    def forward(self, x):
        # x: (batch_size, 256*256*4 + 14)
        
        # 将x拆分为a和b
        a = x[:, :256*256*4]  # 前部分是 a
        b = x[:, 256*256*4:]  # 后部分是 b
        
        # 处理输入a
        a = a.view(-1, 4, 256, 256)  # Reshape为图像大小 (batch_size, 3, 24, 24)
        a_features = self.conv_net(a)
        
        # 处理输入b
        b_features = self.fc_joint(b)
        
        # 拼接a和b的特征
        combined_features = torch.cat((a_features, b_features), dim=1)
        
        # 最终的MLP
        output = self.fc_concat(combined_features)
        return output
    
# 将网络封装为 nn.Sequential 格式
class SequentialWrapper(nn.Module):
    def __init__(self):
        super(SequentialWrapper, self).__init__()
        self.network = SplitAndProcessNetwork()
        self.in_features = 256*256*4 + 38
    
    def forward(self, inputs):
        return self.network(inputs)


class ActorCritic_QIRUI(nn.Module):
    is_recurrent = False

    def __init__(
        self,
        num_actor_obs,
        num_critic_obs,
        num_actions,
        actor_hidden_dims=[256, 256, 256],
        critic_hidden_dims=[256, 256, 256],
        activation="elu",
        init_noise_std=1.0,
        **kwargs,
    ):
        if kwargs:
            print(
                "ActorCritic.__init__ got unexpected arguments, which will be ignored: "
                + str([key for key in kwargs.keys()])
            )
        super().__init__()
        activation = get_activation(activation)

        mlp_input_dim_a = num_actor_obs
        mlp_input_dim_c = num_critic_obs

        # Policy

        actor_layers = []
        actor_layers.append(SequentialWrapper())
        actor_layers.append(nn.Linear(256, num_actions))
        actor_layers.append(nn.Tanh())

        # actor_layers.append(nn.Linear(mlp_input_dim_a, actor_hidden_dims[0]))
        # actor_layers.append(activation)
        # for layer_index in range(len(actor_hidden_dims)):
        #     if layer_index == len(actor_hidden_dims) - 1:
        #         actor_layers.append(nn.Linear(actor_hidden_dims[layer_index], num_actions))
        #     else:
        #         actor_layers.append(nn.Linear(actor_hidden_dims[layer_index], actor_hidden_dims[layer_index + 1]))
        #         actor_layers.append(activation)
        self.actor = nn.Sequential(*actor_layers)

        # Value function
        critic_layers = []
        critic_layers.append(SequentialWrapper())
        critic_layers.append(nn.Linear(256, 1))
        # critic_layers.append(nn.Linear(mlp_input_dim_c, critic_hidden_dims[0]))
        # critic_layers.append(activation)
        # for layer_index in range(len(critic_hidden_dims)):
        #     if layer_index == len(critic_hidden_dims) - 1:
        #         critic_layers.append(nn.Linear(critic_hidden_dims[layer_index], 1))
        #     else:
        #         critic_layers.append(nn.Linear(critic_hidden_dims[layer_index], critic_hidden_dims[layer_index + 1]))
        #         critic_layers.append(activation)
        self.critic = nn.Sequential(*critic_layers)

        print(f"Actor network: {self.actor}")
        print(f"Critic network: {self.critic}")

        # Action noise
        self.std = nn.Parameter(init_noise_std * torch.ones(num_actions))
        self.distribution = None
        # disable args validation for speedup
        Normal.set_default_validate_args = False

        # seems that we get better performance without init
        # self.init_memory_weights(self.memory_a, 0.001, 0.)
        # self.init_memory_weights(self.memory_c, 0.001, 0.)

    @staticmethod
    # not used at the moment
    def init_weights(sequential, scales):
        [
            torch.nn.init.orthogonal_(module.weight, gain=scales[idx])
            for idx, module in enumerate(mod for mod in sequential if isinstance(mod, nn.Linear))
        ]

    def reset(self, dones=None):
        pass

    def forward(self):
        raise NotImplementedError

    @property
    def action_mean(self):
        return self.distribution.mean

    @property
    def action_std(self):
        return self.distribution.stddev

    @property
    def entropy(self):
        return self.distribution.entropy().sum(dim=-1)

    def update_distribution(self, observations):
        mean = self.actor(observations)
        self.distribution = Normal(mean, mean * 0.0 + self.std)

    def act(self, observations, **kwargs):
        self.update_distribution(observations)
        return self.distribution.sample()

    def get_actions_log_prob(self, actions):
        return self.distribution.log_prob(actions).sum(dim=-1)

    def act_inference(self, observations):
        actions_mean = self.actor(observations)
        return actions_mean

    def evaluate(self, critic_observations, **kwargs):
        value = self.critic(critic_observations)
        return value


def get_activation(act_name):
    if act_name == "elu":
        return nn.ELU()
    elif act_name == "selu":
        return nn.SELU()
    elif act_name == "relu":
        return nn.ReLU()
    elif act_name == "crelu":
        return nn.CReLU()
    elif act_name == "lrelu":
        return nn.LeakyReLU()
    elif act_name == "tanh":
        return nn.Tanh()
    elif act_name == "sigmoid":
        return nn.Sigmoid()
    else:
        print("invalid activation function!")
        return None
