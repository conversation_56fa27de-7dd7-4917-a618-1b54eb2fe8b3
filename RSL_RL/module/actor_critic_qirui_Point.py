#  Copyright 2021 ETH Zurich, NVIDIA CORPORATION
#  SPDX-License-Identifier: BSD-3-Clause


from __future__ import annotations

import torch
import torch.nn as nn
from torch.distributions import Normal
import torchvision.transforms as transforms
from torchvision.models import resnet18
import torch.nn.functional as F

# from pointnet2_ops.pointnet2_modules import PointnetSAModule
# import torch
# import torch.nn as nn
# import torch.nn.functional as F
from pytorch3d.ops import sample_farthest_points, ball_query
def robust_group_features_by_idx(features, idx):
    """
    鲁棒的特征分组函数，包含全面的索引验证和修复
    features: (B, C, N)
    idx: (B, M, k)
    return: (B, C, M, k)
    """
    B, C, N = features.shape
    _, M, K = idx.shape
    
    # 确保输入张量在同一设备上
    if features.device != idx.device:
        idx = idx.to(features.device)
    
    # 初始化有效索引掩码
    valid_idx = (idx >= 0) & (idx < N)
    
    # 统计无效索引数量
    invalid_count = (~valid_idx).sum().item()
    if invalid_count > 0:
        print(f"警告: 检测到 {invalid_count} 个无效索引，正在修复...")
        # 将无效索引替换为0（或其他有效索引）
        idx = torch.where(valid_idx, idx, torch.zeros_like(idx))
    
    # 扩展特征和索引
    features_expand = features.unsqueeze(2).expand(-1, -1, M, -1)  # (B, C, M, N)
    idx_expand = idx.unsqueeze(1).expand(-1, C, -1, -1)  # (B, C, M, K)
    
    # 执行安全的gather操作
    try:
        grouped_features = torch.gather(features_expand, dim=3, index=idx_expand)
    except Exception as e:
        print(f"gather操作错误: {e}")
        # 回退方案：创建全0张量
        grouped_features = torch.zeros(B, C, M, K, device=features.device, dtype=features.dtype)
    
    return grouped_features
# def group_features_by_idx(features, idx):
#     """
#     features: (B, C, N)
#     idx: (B, M, k)
#     return: (B, C, M, k)
#     """
#     B, C, N = features.shape
#     _, M, K = idx.shape

#     # 扩展 features 为 (B, C, N) → (B, C, 1, N)
#     features_expand = features.unsqueeze(2).expand(-1, -1, M, -1)  # (B, C, M, N)

#     # 扩展 idx 为 (B, 1, M, k)
#     idx_expand = idx.unsqueeze(1).expand(-1, C, -1, -1)  # (B, C, M, k)

#     # gather 出邻居特征
#     grouped_features = torch.gather(features_expand, dim=3, index=idx_expand)  # (B, C, M, k)
#     return grouped_features

class PointNetSetAbstraction(nn.Module):
    def __init__(self, num_centroids, radius, k, in_channels, out_channels):
        super().__init__()
        self.num_centroids = num_centroids
        self.radius = radius
        self.k = k

        self.mlp = nn.Sequential(
            nn.Conv2d(in_channels, out_channels // 2, 1),
            nn.ReLU(),
            nn.Conv2d(out_channels // 2, out_channels, 1),
            nn.ReLU(),
        )

    def forward(self, xyz, features):
        """
        xyz: (B, N, 3)
        features: (B, C_in, N) or None
        """
        B, N, _ = xyz.shape

        # FPS采样中心点
        centroids, _ = sample_farthest_points(xyz, K=self.num_centroids)  # (B, M, 3)

        # 邻居索引（仅索引）
        dists, idx, grouped_xyz = ball_query(centroids, xyz, K=self.k, radius=self.radius)  # idx: (B, M, k), grouped_xyz: (B, M, k, 3)
        # idx: (B, M, k)
        invalid_mask = (idx == -1)

        # 克隆一份 idx
        safe_idx = idx.clone()

        # 用 idx 的第一个邻居（即 idx[..., 0]）来替代 -1
        # 需要把 idx[..., 0] 广播成和 idx 同形状，才能用 masked_scatter
        ref_val = idx[..., 0].unsqueeze(-1).expand_as(idx)  # (B, M, k)

        # 用 masked_scatter 替换所有 -1 为第一个邻居
        safe_idx = safe_idx.masked_scatter(invalid_mask, ref_val[invalid_mask])

        # 相对位置
        relative_xyz = grouped_xyz - centroids.unsqueeze(2)  # (B, M, k, 3)
        relative_xyz = relative_xyz.permute(0, 3, 1, 2)  # (B, 3, M, k)

        if features is not None:
            # 对 features 做 group：features (B, C, N), idx (B, M, k) → grouped_feats (B, C, M, k)
            # print('features.shape',features.shape)
            # print('idx.shape',idx.shape)
            grouped_feats = robust_group_features_by_idx(features, safe_idx)  # 自定义函数
            # print('grouped_feats.shape',grouped_feats.shape)
            x = torch.cat([relative_xyz, grouped_feats], dim=1)  # (B, 3 + C, M, k)
        else:
            x = relative_xyz  # 仅用位置特征

        # 局部 MLP + 最大池化
        # print('x.shape',x.shape)
        # print(self.mlp)
        x = self.mlp(x)  # (B, out_channels, M, k)
        x = torch.max(x, dim=-1)[0]  # (B, out_channels, M)

        return centroids, x



# class PointNetPP(nn.Module):
#     def __init__(self, output_dim=256):
#         super().__init__()

#         # 第一层：128个中心点，半径0.2，找32邻居
#         self.sa1 = PointNetSetAbstraction(
#             num_centroids=256,
#             radius=0.1,
#             k=32,
#             in_channels=3,      # relative_xyz 维度
#             out_channels=64,
#         )

#         # 第二层：32个中心点，半径0.4，找32邻居
#         self.sa2 = PointNetSetAbstraction(
#             num_centroids=128,
#             radius=0.2,
#             k=32,
#             in_channels=64 + 3,  # 上一层输出 + relative_xyz
#             out_channels=128,
#         )

#         # 第二层：32个中心点，半径0.4，找32邻居
#         self.sa3 = PointNetSetAbstraction(
#             num_centroids=64,
#             radius=0.3,
#             k=32,
#             in_channels=128 + 3,  # 上一层输出 + relative_xyz
#             out_channels=256,
#         )

#         # 最后的全连接层
#         self.global_mlp = nn.Sequential(
#             nn.Linear(256, 256),
#             nn.ReLU(),
#             nn.Linear(256, output_dim),
#             nn.ReLU(),
#         )

#     def forward(self, xyz):
#         """
#         xyz: (B, N, 3)
#         """
#         # 第一层
#         xyz0 = xyz[:, :, :3]
#         feat0 = None
#         xyz1, feat1 = self.sa1(xyz0, features=feat0)  # (B, 128, 3), (B, 128, 128)
#         # print(feat1.shape)
#         # feat1 =feat1.contiguous()
#         # 第二层（带前一层特征）
#         xyz2, feat2 = self.sa2(xyz1, features=feat1)  # (B, 32, 3), (B, 256, 32)

#         # 第三层（带前一层特征）
#         xyz3, feat3 = self.sa3(xyz2, features=feat2)  # (B, 32, 3), (B, 256, 32)

#         # 聚合
#         global_feat = torch.max(feat3, dim=-1)[0]  # (B, 256)

#         # 最后 MLP
#         out = self.global_mlp(global_feat)  # (B, output_dim)
#         return out



class PointNetPP(nn.Module):
    def __init__(self, num_centroids=128, radius=0.05, k=32, output_dim=256):
        super().__init__()
        self.num_centroids = num_centroids
        self.radius = radius
        self.k = k

        # 第一级局部 MLP：增加宽度、使用GELU、加BatchNorm
        self.local_mlp1 = nn.Sequential(
            nn.Conv2d(3, 64, 1),
            nn.BatchNorm2d(64),
            nn.GELU(),
            nn.Conv2d(64, 64, 1),
            nn.BatchNorm2d(64),
            nn.GELU()
        )

        # 第二级局部 MLP：更深 + BatchNorm + Dropout
        self.local_mlp2 = nn.Sequential(
            nn.Conv1d(64, 256, 1),
            nn.BatchNorm1d(256),
            nn.GELU(),
            # nn.Dropout(0.2),
            nn.Conv1d(256, 256, 1),
            nn.BatchNorm1d(256),
            nn.GELU()
        )

        # 全局 MLP：更强非线性 + LayerNorm + Dropout
        self.global_mlp = nn.Sequential(
            nn.LayerNorm(256),
            nn.Linear(256, 128),
            nn.ELU(),
            # nn.Dropout(0.2),
            nn.Linear(128, output_dim),
            nn.ELU(),
        )

    def forward(self, xyz):
        """
        Args:
            xyz: Tensor of shape (B, N, 3)
        Returns:
            Tensor of shape (B, output_dim)
        """
        B, N, _ = xyz.shape

        # 1. FPS 采样中心点
        centroids, _ = sample_farthest_points(xyz, K=self.num_centroids)  # (B, num_centroids, 3)

        # 2. 邻居点查找
        _, _, grouped_xyz = ball_query(centroids, xyz, K=self.k, radius=self.radius)  # (B, num_centroids, k, 3)

        # 3. 点相对中心偏移
        grouped_xyz -= centroids.unsqueeze(2)  # (B, num_centroids, k, 3)

        # 4. 第一级局部特征提取
        features = self.local_mlp1(grouped_xyz.permute(0, 3, 1, 2))  # (B, 128, num_centroids, k)
        features = torch.max(features, dim=-1)[0]  # (B, 128, num_centroids)

        # 5. 第二级局部特征提取
        features = self.local_mlp2(features)  # (B, 128, num_centroids)

        # 6. 全局聚合
        features = torch.max(features, dim=-1)[0]  # (B, 128)

        # 7. Final MLP
        out = self.global_mlp(features)  # (B, output_dim)
        return out

# class PointNetPP(nn.Module):
#     def __init__(self, num_centroids=128, radius=0.2, k=32, output_dim=256, arm_embed_dim=8):
#         super().__init__()
#         self.num_centroids = num_centroids
#         self.radius = radius
#         self.k = k
#         self.arm_embed_dim = arm_embed_dim

#         # local_mlp1 输入通道从 3 → 3+arm_embed_dim
#         self.local_mlp1 = nn.Sequential(
#             nn.Conv2d(3 + arm_embed_dim, 64, 1),
#             nn.BatchNorm2d(64),
#             nn.GELU(),
#             nn.Conv2d(64, 128, 1),
#             nn.BatchNorm2d(128),
#             nn.GELU()
#         )

#         self.local_mlp2 = nn.Sequential(
#             nn.Conv1d(128, 256, 1),
#             nn.BatchNorm1d(256),
#             nn.GELU(),
#             nn.Dropout(0.5),
#             nn.Conv1d(256, 256, 1),
#             nn.BatchNorm1d(256),
#             nn.GELU()
#         )

#         # 最终全连接：再次拼接 arm_embed 向量 → 256+8=264
#         self.global_mlp = nn.Sequential(
#             nn.LayerNorm(256 + arm_embed_dim),
#             nn.Linear(256 + arm_embed_dim, 256),
#             nn.ELU(),
#             nn.Dropout(0.5),
#             nn.Linear(256, output_dim),
#             nn.ELU()
#         )

#     def forward(self, xyz, arm_label: torch.Tensor):
#         """
#         Args:
#             xyz: (B, N, 3)
#             arm_label: (B,2)
#         Returns:
#             (B, output_dim)
#         """
#         B, N, _ = xyz.shape

#         # 1. 计算中心点和邻居点
#         centroids, _ = sample_farthest_points(xyz, K=self.num_centroids)
#         _, _, grouped_xyz = ball_query(centroids, xyz, K=self.k, radius=self.radius)
#         grouped_xyz = grouped_xyz - centroids.unsqueeze(2)  # (B, C, k, 3)

#         # 2. 获取 arm embedding，并广播到每个点
#         arm_embed = arm_label # (B, arm_embed_dim)
#         arm_embed_exp = arm_embed.view(B, self.arm_embed_dim , 1, 1).expand(-1, -1, self.num_centroids, self.k)
#         arm_embed_exp = arm_embed_exp.permute(0, 2, 3, 1)  # (B, C, k, self.arm_embed_dim )

#         # 3. 拼接嵌入 → (B, C, k, 3+2)
#         concat = torch.cat([grouped_xyz, arm_embed_exp], dim=-1)
#         concat = concat.permute(0, 3, 1, 2).contiguous()  # (B, 3+self.arm_embed_dim , C, k)

#         # 4. 局部特征提取
#         features = self.local_mlp1(concat)  # (B, 128, C, k)
#         features = torch.max(features, dim=-1)[0]  # (B, 128, C)
#         features = self.local_mlp2(features)       # (B, 256, C)
#         features = torch.max(features, dim=-1)[0]  # (B, 256)

#         # 5. 拼接 arm embedding → 融合臂侧信息
#         features = torch.cat([features, arm_embed], dim=1)  # (B, 256+self.arm_embed_dim )

#         return self.global_mlp(features)  # (B, output_dim)



# ----------- MobileViTBlock 定义 ------------
# class ConvBNReLU(nn.Module):
#     def __init__(self, in_c, out_c, k=1, s=1, p=0):
#         super().__init__()
#         self.block = nn.Sequential(
#             nn.Conv2d(in_c, out_c, k, s, p, bias=False),
#             nn.BatchNorm2d(out_c),
#             nn.ELU(inplace=True)
#         )
#     def forward(self, x): return self.block(x)

# class MobileViTBlock(nn.Module):
#     def __init__(self, in_c, ffn_dim, heads, depth, patch=4, reduce_c=16):
#         super().__init__()
#         self.patch = patch
#         self.reduce = ConvBNReLU(in_c, reduce_c, 1)
#         self.unfold = nn.Unfold(kernel_size=patch, stride=patch)
#         self.dim = reduce_c * patch * patch
#         self.transformer = nn.Sequential(*[
#             nn.TransformerEncoderLayer(d_model=self.dim, nhead=heads, dim_feedforward=ffn_dim, batch_first=True)
#             for _ in range(depth)
#         ])
#         self.expand = ConvBNReLU(reduce_c, in_c, 1)

#     def forward(self, x):
#         B, C, H, W = x.shape

#         # 补齐为 patch 大小的倍数，防止 fold 报错
#         pad_h = (self.patch - H % self.patch) % self.patch
#         pad_w = (self.patch - W % self.patch) % self.patch
#         x = F.pad(x, (0, pad_w, 0, pad_h), mode="constant", value=0)
#         H_pad, W_pad = x.shape[2:]

#         x = self.reduce(x)
#         patches = self.unfold(x).permute(0, 2, 1)  # (B, N_patches, dim)
#         patches = self.transformer(patches)
#         patches = patches.permute(0, 2, 1)         # (B, dim, N_patches)

#         fold = nn.Fold(output_size=(H_pad, W_pad), kernel_size=self.patch, stride=self.patch)
#         x = fold(patches)
#         x = self.expand(x)
#         return x[:, :, :H, :W]  # 去除 pad

# # ----------- 点云处理模块 ------------
# class PointTransformerNet(nn.Module):
#     def __init__(self, output_dim=256, num_centroids=128, radius=0.2, k=16, patch_size=4):
#         super().__init__()
#         self.num_centroids, self.radius, self.k, self.patch = num_centroids, radius, k, patch_size

#         self.transformer = MobileViTBlock(
#             in_c=3, ffn_dim=128, heads=4, depth=2,
#             patch=patch_size, reduce_c=16
#         )

#         self.local_mlp = nn.Sequential(
#             nn.Conv1d(16, 128, 1),
#             nn.BatchNorm1d(128),
#             nn.ELU()
#         )

#         self.global_mlp = nn.Sequential(
#             nn.LayerNorm(128),
#             nn.Linear(128, 256),
#             nn.ELU(),
#             nn.Linear(256, output_dim)
#         )

#     def forward(self, xyz):
#         B, N, _ = xyz.shape

#         centroids, _ = sample_farthest_points(xyz, K=self.num_centroids)  # (B, C, 3)
#         _, _, grouped = ball_query(centroids, xyz, K=self.k, radius=self.radius)  # (B, C, k, 3)
#         grouped = grouped - centroids.unsqueeze(2)  # (B, C, k, 3)

#         if self.k != self.patch ** 2:
#             raise ValueError(f"k ({self.k}) 必须为 patch_size^2 ({self.patch ** 2})")

#         x = grouped.permute(0, 3, 1, 2).contiguous().view(B, 3, self.num_centroids * self.patch, self.patch)
#         x = self.transformer(x)  # (B, 3, ..., ...)
#         x = x.view(B, 16, self.num_centroids, -1).mean(-1)  # (B, 16, C)
#         x = self.local_mlp(x)  # (B, 128, C)
#         x = torch.max(x, dim=-1)[0]  # (B, 128)
#         return self.global_mlp(x)  # (B, output_dim)



# class SimplePointNetPP(nn.Module):
#     def __init__(self, out_dim=256):
#         super().__init__()
#         self.sa1 = PointnetSAModule(
#             npoint=512, radius=0.2, nsample=32,
#             mlp=[3, 64, 64, 128], use_xyz=True
#         )
#         self.sa2 = PointnetSAModule(
#             npoint=128, radius=0.4, nsample=64,
#             mlp=[128, 128, out_dim], use_xyz=True
#         )

#     def forward(self, xyz):
#         # xyz = xyz.contiguous()
#         l0_points = xyz.transpose(1, 2).contiguous()  # 无额外特征
#         l0_xyz = xyz.contiguous()

#         l1_xyz, l1_points = self.sa1(l0_xyz, l0_points)  # l1_points: (B, 128, 512)
#         l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)  # l2_points: (B, 256, 128)

#         return l2_points.mean(dim=-1)  # → (B, 256)



class ResidualBlock(nn.Module):
    """1D CNN 残差块"""
    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1, padding=1):
        super(ResidualBlock, self).__init__()
        self.conv1 = nn.Conv1d(in_channels, out_channels, kernel_size, stride, padding)
        self.bn1 = nn.BatchNorm1d(out_channels)
        self.conv2 = nn.Conv1d(out_channels, out_channels, kernel_size, stride, padding)
        self.bn2 = nn.BatchNorm1d(out_channels)
        self.relu = nn.ELU()

        # 如果输入通道数不匹配，则用 1x1 卷积调整
        self.shortcut = nn.Conv1d(in_channels, out_channels, kernel_size=1) if in_channels != out_channels else nn.Identity()

    def forward(self, x):
        res = self.shortcut(x)  # Shortcut 连接
        out = self.relu(self.bn1(self.conv1(x)))
        out = self.bn2(self.conv2(out))
        out += res  # 残差连接
        return self.relu(out)

class Conv1DModel(nn.Module):
    """深层 ResNet 风格的 1D CNN"""
    def __init__(self, in_channels=1, num_residual_blocks=4, hidden_dim=256, embedding_dim=256):
        super(Conv1DModel, self).__init__()
        
        # 初始卷积层
        self.initial_conv = nn.Conv1d(in_channels, hidden_dim, kernel_size=7, padding=3)
        self.initial_bn = nn.BatchNorm1d(hidden_dim)
        self.initial_relu = nn.ELU()

        # 堆叠多个残差块
        self.residual_blocks = nn.Sequential(
            *[ResidualBlock(hidden_dim, hidden_dim) for _ in range(num_residual_blocks)]
        )

        self.pool = nn.AdaptiveAvgPool1d(1)

        # SE模块
        self.se = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 4),
            nn.ELU(),
            nn.Linear(hidden_dim // 4, hidden_dim),
            nn.Sigmoid()
        )

        # Global Average Pooling + FC
        # self.gap = nn.AdaptiveAvgPool1d(7)  # GAP 降维
        # self.fc = nn.Linear(hidden_dim, embedding_dim) # 64 → 256
        self.fc = nn.Sequential(nn.Linear(hidden_dim, embedding_dim),nn.ELU() ) # 64 → 256

    def forward(self, x):
        x = x.unsqueeze(1)  # 增加通道维度
        x = self.initial_relu(self.initial_bn(self.initial_conv(x)))  # 初始卷积
        
        x = self.residual_blocks(x)  # 多层残差块
        # print("x.shape",x.shape)
        # SE 注意力
        x = self.pool(x).squeeze(-1)  # GAP 降维
        # print("x.shape",x.shape)
        se_weight = self.se(x)
        x = x * se_weight
        
        # x = self.gap(x).squeeze(-1)  # GAP 降维
        x = self.fc(x)  # 全连接层转换到 256 维
        return x


# class Nets(nn.Module):
#     """深层 ResNet 风格的 1D CNN"""  # object_up_dim:64 → 21
#     def __init__(self):
#         super(Nets, self).__init__()


#         self.process_joints_left = Conv1DModel()
#         self.process_joints_right = Conv1DModel()

#         self.process_points_left = PointNetPP()
#         self.process_points_right = PointNetPP()

#         self.last_process_Left = nn.Sequential(
#             nn.Linear(256 + 256, 256),
#             nn.ELU(),
#             nn.Dropout(0.2),
#             nn.Linear(256, 256),
#             nn.ELU(),
#             nn.Dropout(0.2),
#             )
        
#         self.last_process_Right = nn.Sequential(
#             nn.Linear(256 + 256, 256),
#             nn.ELU(),
#             nn.Dropout(0.2),
#             nn.Linear(256, 256),
#             nn.ELU(),
#             nn.Dropout(0.2),
#             )
        

#     def forward(self, x):
#         flag = x[:,0].clone().type(torch.int64)

#         x1 = x[:,1:22]
#         x2 = x[:,22:]
#         x2 = x2.view(-1, 800, 3)

#         # x0 = self.process_flag(x0)
#         # x0 = self.arm_embedding(flag)

#         # x1 = torch.cat([x0, x1], dim=1)
#         x_11 = torch.empty([len(flag),256],device=x.device)
#         x_11[flag==0] = self.process_joints_right(x1[flag==0])
#         x_11[flag==1] = self.process_joints_left(x1[flag==1])


#         x_22 = torch.empty([len(flag),256],device=x.device)
#         x_22[flag==0] = self.process_points_right(x2[flag==0])
#         x_22[flag==1] = self.process_points_left(x2[flag==1])

#         # x2_con = x2 
#         x_12 = torch.cat((x_11, x_22), dim=1)
#         # x = self.gap(x).squeeze(-1)  # GAP 降维
#         # for block in self.last_process:
#         #     x_last = block(x_last)  # 全连接层转换到 256 维

#         x_last = torch.empty([len(flag),256],device=x.device)
#         x_last[flag==0] = self.last_process_Right(x_12[flag==0,:])
#         x_last[flag==1] = self.last_process_Left(x_12[flag==1,:])

#         return x_last


class Nets(nn.Module):
    """深层 ResNet 风格的 1D CNN"""  # object_up_dim:64 → 21
    def __init__(self):
        super(Nets, self).__init__()

        # self.process_flag = nn.ModuleList([
        #     nn.Sequential(nn.Linear(2, 256), nn.ELU()),
        # ]+[nn.Sequential(nn.Linear(256, 256),nn.ELU(),nn.Dropout(0.2)) for _ in range(2)]
        # )
        # 机械臂标识嵌入层
        self.arm_embedding = nn.Embedding(2, 32)  # 0:左臂, 1:右臂

        self.process_joints = Conv1DModel(embedding_dim=32)

        self.process_points = PointNetPP(output_dim=32)

        # self.last_process_Left = nn.Sequential(
        #     nn.Linear(256 + 256, 256),
        #     nn.ELU(),
        #     nn.Dropout(0.2),
        #     nn.Linear(256, 256),
        #     nn.ELU(),
        #     nn.Dropout(0.2),
        #     )

        self.last_process = Conv1DModel()
        
        # self.last_process = nn.Sequential(
        #     nn.Linear(256, 256),
        #     nn.ELU(),
        #     # nn.Dropout(0.2),
        #     nn.Linear(256, 256),
        #     nn.ELU(),
        #     # nn.Dropout(0.2),
        #     )
        

    def forward(self, x):
        flag = x[:,0].clone().type(torch.int64)
        x_0 = self.arm_embedding(flag)


        x1 = x[:,1:22]
        x_1 = self.process_joints(x1)
        
        x2 = x[:,22:]
        x2 = x2.view(-1, 800, 3)
        x_2 = self.process_points(x2)



        # x1 = torch.cat([x0, x1], dim=1)

                # x2_con = x2 
        x_012 = torch.cat((x_0,x_1, x_2), dim=1)




        x_last = self.last_process(x_012)

        # x_last = torch.empty([len(flag),256],device=x.device)
        # x_last[flag==0] = self.last_process_Right(x_12[flag==0,:])
        # x_last[flag==1] = self.last_process_Left(x_12[flag==1,:])

        return x_last
     
# 将网络封装为 nn.Sequential 格式
class SequentialWrapper(nn.Module):
    def __init__(self):
        super(SequentialWrapper, self).__init__()
        self.network = Nets()
        self.in_features = 22 + 3*800
    
    def forward(self, inputs):
        return self.network(inputs)


class ActorCritic_QIRUI_POINT(nn.Module):
    is_recurrent = False

    def __init__(
        self,
        num_actor_obs,
        num_critic_obs,
        num_actions,
        actor_hidden_dims=[256, 256, 256],
        critic_hidden_dims=[256, 256, 256],
        activation="elu",
        init_noise_std=1.0,
        **kwargs,
    ):
        if kwargs:
            print(
                "ActorCritic.__init__ got unexpected arguments, which will be ignored: "
                + str([key for key in kwargs.keys()])
            )
        super().__init__()
        activation = get_activation(activation)

        mlp_input_dim_a = num_actor_obs
        mlp_input_dim_c = num_critic_obs

        # Policy



        actor_layers = []
        actor_layers.append(SequentialWrapper())
        actor_layers.append(nn.Linear(256, num_actions))
        actor_layers.append(nn.Tanh())

        # actor_layers.append(nn.Linear(mlp_input_dim_a, actor_hidden_dims[0]))
        # actor_layers.append(activation)
        # for layer_index in range(len(actor_hidden_dims)):
        #     if layer_index == len(actor_hidden_dims) - 1:
        #         actor_layers.append(nn.Linear(actor_hidden_dims[layer_index], num_actions))
        #     else:
        #         actor_layers.append(nn.Linear(actor_hidden_dims[layer_index], actor_hidden_dims[layer_index + 1]))
        #         actor_layers.append(activation)
        self.actor = nn.Sequential(*actor_layers)

        # Value function
        critic_layers = []
        critic_layers.append(SequentialWrapper())
        critic_layers.append(nn.Linear(256, 1))
        # critic_layers.append(nn.Linear(mlp_input_dim_c, critic_hidden_dims[0]))
        # critic_layers.append(activation)
        # for layer_index in range(len(critic_hidden_dims)):
        #     if layer_index == len(critic_hidden_dims) - 1:
        #         critic_layers.append(nn.Linear(critic_hidden_dims[layer_index], 1))
        #     else:
        #         critic_layers.append(nn.Linear(critic_hidden_dims[layer_index], critic_hidden_dims[layer_index + 1]))
        #         critic_layers.append(activation)
        self.critic = nn.Sequential(*critic_layers)

        print(f"Actor network: {self.actor}")
        print(f"Critic network: {self.critic}")

        # Action noise
        self.std = nn.Parameter(init_noise_std * torch.ones(num_actions))
        self.distribution = None
        # disable args validation for speedup
        Normal.set_default_validate_args = False

        # seems that we get better performance without init
        # self.init_memory_weights(self.memory_a, 0.001, 0.)
        # self.init_memory_weights(self.memory_c, 0.001, 0.)

    @staticmethod
    # not used at the moment
    def init_weights(sequential, scales):
        [
            torch.nn.init.orthogonal_(module.weight, gain=scales[idx])
            for idx, module in enumerate(mod for mod in sequential if isinstance(mod, nn.Linear))
        ]

    def reset(self, dones=None):
        pass

    def forward(self):
        raise NotImplementedError

    @property
    def action_mean(self):
        return self.distribution.mean

    @property
    def action_std(self):
        return self.distribution.stddev

    @property
    def entropy(self):
        return self.distribution.entropy().sum(dim=-1)

    def update_distribution(self, observations):
        mean = self.actor(observations)
        self.distribution = Normal(mean, mean * 0.0 + self.std)

    def act(self, observations, **kwargs):
        self.update_distribution(observations)
        return self.distribution.sample()

    def get_actions_log_prob(self, actions):
        return self.distribution.log_prob(actions).sum(dim=-1)

    def act_inference(self, observations):
        actions_mean = self.actor(observations)
        return actions_mean

    def evaluate(self, critic_observations, **kwargs):
        value = self.critic(critic_observations)
        return value


def get_activation(act_name):
    if act_name == "elu":
        return nn.ELU()
    elif act_name == "selu":
        return nn.SELU()
    elif act_name == "relu":
        return nn.ReLU()
    elif act_name == "crelu":
        return nn.CReLU()
    elif act_name == "lrelu":
        return nn.LeakyReLU()
    elif act_name == "tanh":
        return nn.Tanh()
    elif act_name == "sigmoid":
        return nn.Sigmoid()
    else:
        print("invalid activation function!")
        return None
