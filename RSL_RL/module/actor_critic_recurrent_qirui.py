#  Copyright 2021 ETH Zurich, NVIDIA CORPORATION
#  SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.transforms as transforms
from torchvision.models import resnet18
from RSL_RL.module.actor_critic_qirui_PB import ActorCritic_QIRUI_PB, get_activation
from RSL_RL.utils import unpad_trajectories


class ActorCriticRecurrent_QIRUI(ActorCritic_QIRUI_PB):
    is_recurrent = True

    def __init__(
        self,
        num_actor_obs,
        num_critic_obs,
        num_actions,
        actor_hidden_dims=[256, 256, 256],
        critic_hidden_dims=[256, 256, 256],
        activation="elu",
        rnn_type="lstm",
        rnn_hidden_size=128,
        rnn_num_layers=1,
        init_noise_std=1.0,
        **kwargs,
    ):
        if kwargs:
            print(
                "ActorCriticRecurrent.__init__ got unexpected arguments, which will be ignored: " + str(kwargs.keys()),
            )

        super().__init__(
            num_actor_obs=rnn_hidden_size,
            num_critic_obs=rnn_hidden_size,
            num_actions=num_actions,
            actor_hidden_dims=actor_hidden_dims,
            critic_hidden_dims=critic_hidden_dims,
            activation=activation,
            init_noise_std=init_noise_std,
        )

        activation = get_activation(activation)

        self.memory_a = Memory(128, type=rnn_type, num_layers=rnn_num_layers, hidden_size=rnn_hidden_size)
        self.memory_c = Memory(128, type=rnn_type, num_layers=rnn_num_layers, hidden_size=rnn_hidden_size)

        print(f"Actor RNN: {self.memory_a}")
        print(f"Critic RNN: {self.memory_c}")

    def reset(self, dones=None):
        self.memory_a.reset(dones)
        self.memory_c.reset(dones)

    def act(self, observations, masks=None, hidden_states=None):
        input_a = self.memory_a(observations, masks, hidden_states)
        return super().act(input_a.squeeze(0))

    def act_inference(self, observations):
        input_a = self.memory_a(observations)
        return super().act_inference(input_a.squeeze(0))

    def evaluate(self, critic_observations, masks=None, hidden_states=None):
        input_c = self.memory_c(critic_observations, masks, hidden_states)
        return super().evaluate(input_c.squeeze(0))

    def get_hidden_states(self):
        return self.memory_a.hidden_states, self.memory_c.hidden_states


class Memory(torch.nn.Module):
    def __init__(self, input_size, type="lstm", num_layers=1, hidden_size=128):
        super().__init__()
        # CNN+MLP
        self.cnn_mlp = SequentialWrapper()

        # RNN
        rnn_cls = nn.GRU if type.lower() == "gru" else nn.LSTM
        self.rnn = rnn_cls(input_size=input_size, hidden_size=hidden_size, num_layers=num_layers)
        self.hidden_states = None

    def forward(self, input, masks=None, hidden_states=None):
        batch_mode = masks is not None
        if batch_mode:
            # batch mode (policy update): need saved hidden states
            if hidden_states is None:
                raise ValueError("Hidden states not passed to memory module during policy update")
            d0 = input.shape[0]
            d1 = input.shape[1]
            d2 = input.shape[2]
            Feature = self.cnn_mlp(input.reshape(-1,d2))
            out, _ = self.rnn(Feature.view(d0,d1,128), hidden_states)
            out = unpad_trajectories(out, masks)
        else:
            Feature = self.cnn_mlp(input)
            # inference mode (collection): use hidden states of last step
            out, self.hidden_states = self.rnn(Feature.unsqueeze(0), self.hidden_states)
        return out

    def reset(self, dones=None):
        # When the RNN is an LSTM, self.hidden_states_a is a list with hidden_state and cell_state
        for hidden_state in self.hidden_states:
            hidden_state[..., dones, :] = 0.0


class BasicBlock(nn.Module):
    def __init__(self,in_channels,out_channels,stride=[1,1],padding=1) -> None:
        super(BasicBlock, self).__init__()
        # 残差部分
        self.layer = nn.Sequential(
            nn.Conv2d(in_channels,out_channels,kernel_size=3,stride=stride[0],padding=padding,bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True), # 原地替换 节省内存开销
            nn.Conv2d(out_channels,out_channels,kernel_size=3,stride=stride[1],padding=padding,bias=False),
            nn.BatchNorm2d(out_channels)
        )

        # shortcut 部分
        # 由于存在维度不一致的情况 所以分情况
        self.shortcut = nn.Sequential()
        if stride[0] != 1 or in_channels != out_channels:
            self.shortcut = nn.Sequential(
                # 卷积核为1*1 进行升降维
                nn.Conv2d(in_channels, out_channels, kernel_size=1, stride=stride[0], bias=False),
                nn.BatchNorm2d(out_channels)
            )

    def forward(self, x):
        out = self.layer(x)
        out += self.shortcut(x)
        return out


class SplitAndProcessNetwork(nn.Module):
    def __init__(self):
        super(SplitAndProcessNetwork, self).__init__()

        self.model = resnet18(pretrained=True)

        num_ftrs = self.model.fc.in_features
        self.model.fc = nn.Linear(num_ftrs, 512)

        # 冻结前 3 层（conv1、layer1、layer2、layer3）
        for name, param in self.model.named_parameters():
            if name.startswith("conv1") or name.startswith("bn1") or name.startswith("layer1") or name.startswith("layer2") or name.startswith("layer3"):
                param.requires_grad = False  # 冻结参数，不更新
            else:
                param.requires_grad = True  # 允许训练layer4 和 fc

        # 子网络11：处理输入a的卷积网络
        self.conv_net2 = nn.Sequential(
            # 卷积一层
            nn.Conv2d(1, 16, kernel_size=7, stride=2, padding=3),  
            nn.AvgPool2d(kernel_size=2),  
            nn.ReLU(inplace=True),

            # 卷积二层
            BasicBlock(16,16,stride=[1,1]),           
            nn.AvgPool2d(kernel_size=2),               
            nn.ReLU(inplace=True),

            # 卷积三层
            BasicBlock(16,32,stride=[1,1]),       
            nn.AvgPool2d(kernel_size=2),  
            nn.ReLU(inplace=True),

            # 卷积四层
            BasicBlock(32,32,stride=[1,1]), 
            nn.AvgPool2d(kernel_size=2),     
            nn.ReLU(inplace=True),
            nn.Flatten(),  # 展平，输出 

            nn.Linear(32 * 7 * 7, 512),
            # 关节角度的全连接网络部分       
        )
        
        # 子网络2：处理输入b的MLP
        self.fc_joint = nn.Sequential(
            nn.Linear(42, 512),
            nn.ReLU(),
            nn.Linear(512, 512),
            nn.ReLU()
        )

        # self.left_joint_t = nn.TransformerEncoder(nn.TransformerEncoderLayer(3,8,dim_feedforward=256),num_layers=6)
        # self.right_joint_t = nn.TransformerEncoder(nn.TransformerEncoderLayer(3,8,dim_feedforward=256),num_layers=6)
        
        # 合并后的全连接部分
        self.fc_concat = nn.Sequential(
            nn.Linear(512 + 512 + 512, 512),
            nn.ReLU(),
            nn.Linear(512, 128),
            nn.ReLU()
        )

    def forward(self, x):
        # x: (batch_size, 256*256*4 + 14)
        
        # 将x拆分为a和b
        a1 = x[:, :96*256*3]  # 前部分是 a
        a2 = x[:, 96*256*3:96*256*4]  # 前部分是 a
        b = x[:, 96*256*4:]  # 后部分是 b
        # print("x.shape:", x.shape)
        # print("a.shape:", a.shape)
        
        # 处理输入a
        image_tensor = a1.view(-1,96,256,3).permute(0,3,1,2)
        resize_transform = transforms.Resize((224, 224))  # 调整为 100x150 的大小
        image_tensor = resize_transform(image_tensor)
        a_features = self.model(image_tensor)

        # a2 = a2.view(-1, 1, 256, 256) 
        # a_features = self.conv_net1(torch.concat((image_tensor,a2),dim=1))

        # a = a.view(-1, 3, 256, 256)  # Reshape为图像大小 (batch_size, 3, 24, 24)
        # a_features = self.conv_net(a)

        # 处理输入a2
        a2 = a2.view(-1, 1, 96, 256)  # Reshape为图像大小 (batch_size, 3, 24, 24)
        a2 = resize_transform(a2)
        a_features2 = self.conv_net2(a2)
        
        # 处理输入b
        # b_left = b[:,:21].view(-1,7,3).permute(1,0,2)
        # b_left_feature = self.left_joint_t(b_left).permute(1,0,2).view(-1,21)
        # b_right = b[:,21:].view(-1,7,3).permute(1,0,2)
        # b_right_feature = self.right_joint_t(b_right).permute(1,0,2).view(-1,21)
        # b_features = self.fc_joint(torch.concat((b_left_feature,b_right_feature),dim=1))

        b_features = self.fc_joint(b)
        
        # 拼接a和b的特征
        combined_features = torch.cat((a_features, a_features2, b_features), dim=1)
        
        # 最终的MLP
        output = self.fc_concat(combined_features)

        return output

class ResidualBlock(nn.Module):
    """1D CNN 残差块"""
    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1, padding=1):
        super(ResidualBlock, self).__init__()
        self.conv1 = nn.Conv1d(in_channels, out_channels, kernel_size, stride, padding)
        self.bn1 = nn.BatchNorm1d(out_channels)
        self.conv2 = nn.Conv1d(out_channels, out_channels, kernel_size, stride, padding)
        self.bn2 = nn.BatchNorm1d(out_channels)
        self.relu = nn.ReLU()

        # 如果输入通道数不匹配，则用 1x1 卷积调整
        self.shortcut = nn.Conv1d(in_channels, out_channels, kernel_size=1) if in_channels != out_channels else nn.Identity()

    def forward(self, x):
        res = self.shortcut(x)  # Shortcut 连接
        out = self.relu(self.bn1(self.conv1(x)))
        out = self.bn2(self.conv2(out))
        out += res  # 残差连接
        return self.relu(out)

class Conv1DModel(nn.Module):
    """深层 ResNet 风格的 1D CNN"""
    def __init__(self, in_channels=1, num_residual_blocks=4, hidden_dim=128, embedding_dim=128):
        super(Conv1DModel, self).__init__()
        
        # 初始卷积层
        self.initial_conv = nn.Conv1d(in_channels, hidden_dim, kernel_size=7, padding=3)
        self.initial_bn = nn.BatchNorm1d(hidden_dim)
        self.initial_relu = nn.ReLU()

        # 堆叠多个残差块
        self.residual_blocks = nn.Sequential(
            *[ResidualBlock(hidden_dim, hidden_dim) for _ in range(num_residual_blocks)]
        )

        self.pool = nn.AdaptiveAvgPool1d(1)

        # SE模块
        self.se = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 4),
            nn.ReLU(),
            nn.Linear(hidden_dim // 4, hidden_dim),
            nn.Sigmoid()
        )

        # Global Average Pooling + FC
        # self.gap = nn.AdaptiveAvgPool1d(7)  # GAP 降维
        self.fc = nn.Linear(hidden_dim, embedding_dim)  # 64 → 256

    def forward(self, x):
        x = x.unsqueeze(1)  # 增加通道维度
        x = self.initial_relu(self.initial_bn(self.initial_conv(x)))  # 初始卷积
        
        x = self.residual_blocks(x)  # 多层残差块
        # print("x.shape",x.shape)
        # SE 注意力
        x = self.pool(x).squeeze(-1)  # GAP 降维
        # print("x.shape",x.shape)
        se_weight = self.se(x)
        x = x * se_weight
        
        # x = self.gap(x).squeeze(-1)  # GAP 降维
        x = self.fc(x)  # 全连接层转换到 256 维
        return x
    
# 将网络封装为 nn.Sequential 格式
class SequentialWrapper(nn.Module):
    def __init__(self):
        super(SequentialWrapper, self).__init__()
        self.network = Conv1DModel()
        self.in_features = 48
    
    def forward(self, inputs):
        return self.network(inputs)
