#  Copyright 2021 ETH Zurich, NVIDIA CORPORATION
#  SPDX-License-Identifier: BSD-3-Clause


from __future__ import annotations

import torch
import torch.nn as nn
from torch.distributions import Normal
from torchvision.models import resnet18
import torch.nn.functional as F
from torch import nan_to_num, isfinite

class BasicBlock(nn.Module):
    def __init__(self,in_channels,out_channels,stride=[1,1],padding=1) -> None:
        super(BasicBlock, self).__init__()
        # 残差部分
        self.layer = nn.Sequential(
            nn.Conv2d(in_channels,out_channels,kernel_size=3,stride=stride[0],padding=padding,bias=False),
            # nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True), # 原地替换 节省内存开销
            nn.Conv2d(out_channels,out_channels,kernel_size=3,stride=stride[1],padding=padding,bias=False),
            # nn.BatchNorm2d(out_channels)
        )

        # shortcut 部分
        # 由于存在维度不一致的情况 所以分情况
        self.shortcut = nn.Sequential()
        if stride[0] != 1 or in_channels != out_channels:
            self.shortcut = nn.Sequential(
                # 卷积核为1*1 进行升降维
                nn.Conv2d(in_channels, out_channels, kernel_size=1, stride=stride[0], bias=False),
                # nn.BatchNorm2d(out_channels)
            )

    def forward(self, x):
        out = self.layer(x)
        out += self.shortcut(x)
        return out

class SplitAndProcessNetwork(nn.Module):
    def __init__(self):
        super(SplitAndProcessNetwork, self).__init__()
        
        # 子网络1：处理输入a的卷积网络
        # self.conv_net = nn.Sequential(
        #     # 卷积一层
        #     # nn.Conv2d(3, 16, kernel_size=3, stride=1, padding=1),  # 256x256x4 -> 128x128x16
        #     BasicBlock(3,16),
        #     nn.MaxPool2d(kernel_size=2),  # 128x128x16 -> 64x64x16
        #     nn.ReLU(inplace=True),
        #     # 卷积二层
        #     # nn.Conv2d(16, 32, kernel_size=3, stride=1, padding=1),  # 64x64x16 -> 32x32x32
        #     BasicBlock(16,32),
        #     nn.MaxPool2d(kernel_size=2),  # 32x32x32 -> 16x16x32
        #     nn.ReLU(inplace=True),
        #     # 卷积三层
        #     # nn.Conv2d(32, 64, kernel_size=3, stride=1, padding=1),  # 16x16x64 -> 8x8x64
        #     BasicBlock(32,32),
        #     nn.MaxPool2d(kernel_size=2),  # 8x8x64 -> 4x4x64
        #     nn.ReLU(inplace=True),
        #     # 卷积四层
        #     # nn.Conv2d(64, 128, kernel_size=3, stride=1, padding=1),  # 4x4x64 -> 2x2x128
        #     BasicBlock(32,64),
        #     nn.ReLU(inplace=True),
        #     nn.MaxPool2d(kernel_size=2),  # 8x8x64 -> 4x4x64

        #     BasicBlock(64,64),
        #     nn.ReLU(inplace=True),
        #     nn.MaxPool2d(kernel_size=2),  # 8x8x64 -> 4x4x64

        #     nn.Flatten(),  # 展平，输出 (2*2*128=512)

        #     nn.Linear(64 * 7 * 7, 256),  # 调整到 512 维
        #     nn.ReLU(inplace=True),
        #     # 关节角度的全连接网络部分       
        # )

        # 子网络11：处理输入a的卷积网络
        self.conv_net2 = nn.Sequential(
            # 卷积一层
            # nn.Conv2d(1, 16, kernel_size=3, stride=1, padding=1),  # 256x256x4 -> 128x128x16
            BasicBlock(1,16),
            nn.MaxPool2d(kernel_size=2),  # 128x128x16 -> 64x64x16
            nn.ReLU(inplace=True),
            # 卷积二层
            # nn.Conv2d(16, 32, kernel_size=3, stride=1, padding=1),  # 64x64x16 -> 32x32x32
            BasicBlock(16,32),
            nn.MaxPool2d(kernel_size=2),  # 32x32x32 -> 16x16x32
            nn.ReLU(inplace=True),
            # 卷积三层
            # nn.Conv2d(32, 32, kernel_size=3, stride=1, padding=1),  # 16x16x32 -> 8x8x32
            BasicBlock(32,32),
            nn.MaxPool2d(kernel_size=2),  # 8x8x64 -> 4x4x64
            nn.ReLU(inplace=True),
            # 卷积四层
            # nn.Conv2d(32, 32, kernel_size=3, stride=1, padding=1),  # 4x4x632 -> 2x2x32
            BasicBlock(32,32),
            nn.ReLU(),
            nn.MaxPool2d(kernel_size=2),  # 8x8x64 -> 4x4x64

            BasicBlock(32,32),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2),  # 8x8x64 -> 4x4x64

            nn.Flatten(),  # 展平，输出 (2*2*128=512)

            nn.Linear(32 * 7 * 7, 256),  # 调整到 512 维
            nn.ReLU(inplace=True),
            # 关节角度的全连接网络部分       
        )
        # 加载预训练模型

        # self.model = resnet18(pretrained=True)

        # num_ftrs = self.model.fc.in_features
        # self.model.fc = nn.Linear(num_ftrs, 512)

        # # 冻结前 3 层（conv1、layer1、layer2、layer3）
        # for name, param in self.model.named_parameters():
        #     if name.startswith("conv1") or name.startswith("bn1") or name.startswith("layer1") or name.startswith("layer2") or name.startswith("layer3"):
        #         param.requires_grad = False  # 冻结参数，不更新
        #     else:
        #         param.requires_grad = True  # 允许训练layer4 和 fc

        
        # 子网络2：处理输入b的MLP
        self.fc_joint = nn.Sequential(
            nn.Linear(28, 512),
            nn.ReLU(inplace=True),
            nn.Linear(512, 256),
            nn.ReLU(inplace=True)
        )
        
        # 合并后的全连接部分
        self.fc_concat = nn.Sequential(
            nn.Linear(256 + 256, 512),
            nn.ReLU(inplace=True),
            nn.Linear(512, 256),
            nn.ReLU(inplace=True)
        )

    def forward(self, x):
        # x: (batch_size, 256*256*4 + 14)
        
        # 将x拆分为a和b
        # a = x[:, :224*224*3]  # 前部分是 a
        a2 = x[:, :224*224*1]  # 前部分是 a
        b = x[:, 224*224*1:]  # 后部分是 b
        
        # 处理输入a
        # image_tensor = F.interpolate(a.view(-1,256,256,3).permute(0,3,1,2), size=(224, 224), mode='bilinear', align_corners=False)  # 调整大小
        # a_features = self.model(image_tensor)
        # a = a.view(-1,3,224,224) # Reshape为图像大小 (batch_size, 3, 256, 256)
        # a_features = self.conv_net(a)

        # 处理输入a
        a2 = a2.view(-1, 1, 224, 224)  # Reshape为图像大小 (batch_size, 1, 224, 224)
        a_features2 = self.conv_net2(a2)
        
        # 处理输入b
        b_features = self.fc_joint(b)
        
        # 拼接a和b的特征
        combined_features = torch.cat((a_features2, b_features), dim=1)
        
        # 最终的MLP
        output = self.fc_concat(combined_features)
        return output
    
# 将网络封装为 nn.Sequential 格式
class SequentialWrapper(nn.Module):
    def __init__(self):
        super(SequentialWrapper, self).__init__()
        self.network = SplitAndProcessNetwork()
        self.in_features = 224*224*1 + 28
    
    def forward(self, inputs):
        return self.network(inputs)


class ActorCritic_QIJIANG(nn.Module):
    is_recurrent = False

    def __init__(
        self,
        num_actor_obs,
        num_critic_obs,
        num_actions,
        actor_hidden_dims=[256, 256, 256],
        critic_hidden_dims=[256, 256, 256],
        activation="elu",
        init_noise_std=1.0,
        **kwargs,
    ):
        if kwargs:
            print(
                "ActorCritic.__init__ got unexpected arguments, which will be ignored: "
                + str([key for key in kwargs.keys()])
            )
        super().__init__()
        activation = get_activation(activation)

        mlp_input_dim_a = num_actor_obs
        mlp_input_dim_c = num_critic_obs

        # Policy

        actor_layers = []
        actor_layers.append(SequentialWrapper())
        actor_layers.append(nn.Linear(256, num_actions))
        actor_layers.append(nn.Tanh())

        # actor_layers.append(nn.Linear(mlp_input_dim_a, actor_hidden_dims[0]))
        # actor_layers.append(activation)
        # for layer_index in range(len(actor_hidden_dims)):
        #     if layer_index == len(actor_hidden_dims) - 1:
        #         actor_layers.append(nn.Linear(actor_hidden_dims[layer_index], num_actions))
        #     else:
        #         actor_layers.append(nn.Linear(actor_hidden_dims[layer_index], actor_hidden_dims[layer_index + 1]))
        #         actor_layers.append(activation)
        self.actor = nn.Sequential(*actor_layers)

        # Value function
        critic_layers = []
        critic_layers.append(SequentialWrapper())
        critic_layers.append(nn.Linear(256, 1))
        # critic_layers.append(nn.Linear(mlp_input_dim_c, critic_hidden_dims[0]))
        # critic_layers.append(activation)
        # for layer_index in range(len(critic_hidden_dims)):
        #     if layer_index == len(critic_hidden_dims) - 1:
        #         critic_layers.append(nn.Linear(critic_hidden_dims[layer_index], 1))
        #     else:
        #         critic_layers.append(nn.Linear(critic_hidden_dims[layer_index], critic_hidden_dims[layer_index + 1]))
        #         critic_layers.append(activation)
        self.critic = nn.Sequential(*critic_layers)

        print(f"Actor network: {self.actor}")
        print(f"Critic network: {self.critic}")

        # Action noise
        self.std = nn.Parameter(init_noise_std * torch.ones(num_actions))
        self.distribution = None
        # disable args validation for speedup
        Normal.set_default_validate_args = False

        # seems that we get better performance without init
        # self.init_memory_weights(self.memory_a, 0.001, 0.)
        # self.init_memory_weights(self.memory_c, 0.001, 0.)

    @staticmethod
    # not used at the moment
    def init_weights(sequential, scales):
        [
            torch.nn.init.orthogonal_(module.weight, gain=scales[idx])
            for idx, module in enumerate(mod for mod in sequential if isinstance(mod, nn.Linear))
        ]

    def reset(self, dones=None):
        pass

    def forward(self):
        raise NotImplementedError

    @property
    def action_mean(self):
        return self.distribution.mean

    @property
    def action_std(self):
        return self.distribution.stddev

    @property
    def entropy(self):
        return self.distribution.entropy().sum(dim=-1)

    def update_distribution(self, observations):
        if torch.isnan(observations).any() or torch.isinf(observations).any():
            print("观测值包含非法值(NaN/inf)")
        mean = self.actor(observations)
        if torch.isnan(mean).any() or torch.isinf(mean).any():
            print("mean包含非法值(NaN/inf)")
        self.distribution = Normal(mean, mean * 0.0 + self.std)

    def act(self, observations, **kwargs):
        # if not isfinite(observations).all():
        #     raise ValueError("观测值包含非法值(NaN/inf)")
        self.update_distribution(observations)
        return self.distribution.sample()

    def get_actions_log_prob(self, actions):
        return self.distribution.log_prob(actions).sum(dim=-1)

    def act_inference(self, observations):
        actions_mean = self.actor(observations)
        return actions_mean

    def evaluate(self, critic_observations, **kwargs):
        value = self.critic(critic_observations)
        return value


def get_activation(act_name):
    if act_name == "elu":
        return nn.ELU()
    elif act_name == "selu":
        return nn.SELU()
    elif act_name == "relu":
        return nn.ReLU()
    elif act_name == "crelu":
        return nn.CReLU()
    elif act_name == "lrelu":
        return nn.LeakyReLU()
    elif act_name == "tanh":
        return nn.Tanh()
    elif act_name == "sigmoid":
        return nn.Sigmoid()
    else:
        print("invalid activation function!")
        return None
