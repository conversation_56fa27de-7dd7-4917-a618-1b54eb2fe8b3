# qirui_grasp

#### 介绍
强化学习实现人形机器人抓取

#### 软件架构
机器人的导入，观测值，奖励函数，网络，文件架构
agents: 
rsl_rl_ppo_QiruiItem_cfg.py 训练控制参数
rl_games_QiruiItem_ppo_cfg.yaml 

num_steps_per_env = 128	每个 environment 收集 128 步的 trajectory
max_iterations = 10000	训练总迭代轮数
save_interval = 100	每 100 轮保存一次模型
experiment_name = "QituiItem_direct_Depth"	日志目录名称前缀，最终保存在 logs/QituiItem_direct_Depth
empirical_normalization = False	是否使用经验均值方差做 obs 标准化（关闭代表使用 running mean）


#### 安装教程
sim4.5.0和sim4.2.0版本不同，需要修改部分代码

pip install ./pytorch3d-0.7.8+pt2.5.1cu124-cp310-cp310-linux_x86_64.whl
conda install python-orocos-kdl

1.  xxxx
2.  xxxx
3.  xxxx

#### 使用说明
python -m scripts.train --task=Isaac-QituiItem-Direct-v0 --enable_cameras --headless --num_envs 1
python -m scripts.train --task=Isaac-QituiItem-Direct-v0 --enable_cameras --num_envs 4

1.  xxxx
2.  xxxx
3.  xxxx

#### 参与贡献

1.  Fork 本仓库
2.  新建 Feat_xxx 分支
3.  提交代码
4.  新建 Pull Request


#### 特技

1.  使用 Readme\_XXX.md 来支持不同的语言，例如 Readme\_en.md, Readme\_zh.md
2.  Gitee 官方博客 [blog.gitee.com](https://blog.gitee.com)
3.  你可以 [https://gitee.com/explore](https://gitee.com/explore) 这个地址来了解 Gitee 上的优秀开源项目
4.  [GVP](https://gitee.com/gvp) 全称是 Gitee 最有价值开源项目，是综合评定出的优秀开源项目
5.  Gitee 官方提供的使用手册 [https://gitee.com/help](https://gitee.com/help)
6.  Gitee 封面人物是一档用来展示 Gitee 会员风采的栏目 [https://gitee.com/gitee-stars/](https://gitee.com/gitee-stars/)
